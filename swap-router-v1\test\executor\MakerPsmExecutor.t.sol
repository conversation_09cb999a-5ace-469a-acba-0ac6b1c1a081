/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";

contract MakerPsmExecutorTest is InitialTest {
    function testMakerlitePsmExecutorFromDAIToUSDC() public {
        bytes memory payload =
            abi.encode(0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0, 1e12);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 2, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        uint256 usdcBalanceBefore = IERC20(******************************************).balanceOf(address(this));
        deal(******************************************, address(this), 2333 ether);
        IERC20(******************************************).approve(address(router), 2333 ether);
        router.swap(
            ******************************************,
            2333 ether,
            ******************************************,
            999e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 daiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        console.log("daiBalanceAfter: ", daiBalanceAfter);
        console.log("usdcBalanceAfter: ", usdcBalanceAfter);
        assertEq(usdcBalanceAfter, usdcBalanceBefore + 2333e6);
        assertEq(daiBalanceAfter, 0);
    }

    // Do not use this test case, because the liquidity is not enough in the pool
    // function testMakerPsmExecutorFromDAIToUSDC() public {
    //     bytes memory payload =
    //         abi.encode(******************************************, ******************************************, 0, 1000000000000);
    //     Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
    //     Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 2, payload);
    //     swaps[0] = simpleSwap;
    //     Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
    //     Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
    //     adapters[0] = adapter;
    //     Utils.SinglePath memory singlePath = Utils.SinglePath(******************************************, adapters);
    //     Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
    //     singlePaths[0] = singlePath;
    //     Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
    //     Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
    //     multiPaths[0] = multiPath;
    //     uint256 usdcBalanceBefore = IERC20(******************************************).balanceOf(address(this));
    //     deal(******************************************, address(this), 0 ether);
    //     IERC20(******************************************).approve(address(router), 0 ether);
    //     router.swap(
    //         ******************************************,
    //         0 ether,
    //         ******************************************,
    //         0,
    //         false,
    //         0,
    //         feeReceiver,
    //         multiPaths
    //     );
    //     uint256 daiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
    //     uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
    //     console.log("daiBalanceAfter: ", daiBalanceAfter);
    //     console.log("usdcBalanceAfter: ", usdcBalanceAfter);
    //     assertEq(usdcBalanceAfter, usdcBalanceBefore + 2333e6);
    //     assertEq(daiBalanceAfter, 0);
    // }

    // function testMakerPsmExecutorFromUSDCToDAI() public {
    //     bytes memory payload = abi.encode(******************************************, ******************************************, 0, 1e12);
    //     Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
    //     Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 2, payload);
    //     swaps[0] = simpleSwap;
    //     Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
    //     Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
    //     adapters[0] = adapter;
    //     Utils.SinglePath memory singlePath = Utils.SinglePath(******************************************, adapters);
    //     Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
    //     singlePaths[0] = singlePath;
    //     Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
    //     Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
    //     multiPaths[0] = multiPath;

    //     uint256 daiBalanceBefore = IERC20(******************************************).balanceOf(address(this));
    //     deal(******************************************, address(this), 0);
    //     IERC20(******************************************).approve(address(router), 0);
    //     router.swap(
    //         ******************************************,
    //         0,
    //         ******************************************,
    //         0,
    //         false,
    //         0,
    //         feeReceiver,
    //         multiPaths
    //     );
    //     uint256 daiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
    //     uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
    //     console.log("daiBalanceAfter: ", daiBalanceAfter);
    //     console.log("usdcBalanceAfter: ", usdcBalanceAfter);
    //     assertEq(daiBalanceAfter, daiBalanceBefore + 2333e18);
    //     assertEq(usdcBalanceAfter, 0);
    // }

    function testMakerlitePsmExecutorFromUSDCToDAI() public {
        bytes memory payload =
            abi.encode(0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0, 1e12);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 2, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        uint256 daiBalanceBefore = IERC20(******************************************).balanceOf(address(this));
        deal(******************************************, address(this), 2333e6);
        IERC20(******************************************).approve(address(router), 2333e6);
        router.swap(
            ******************************************,
            2333e6,
            ******************************************,
            999e18,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 daiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        console.log("daiBalanceAfter: ", daiBalanceAfter);
        console.log("usdcBalanceAfter: ", usdcBalanceAfter);
        assertEq(daiBalanceAfter, daiBalanceBefore + 2333e18);
        assertEq(usdcBalanceAfter, 0);
    }

    function testMakerPsmExecutorWrongToken() public {
        bytes memory payload =
            abi.encode(0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0xf6e72Db5454dd049d0788e411b06CfAF16853042, 0, 1e12);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 2, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        deal(******************************************, address(this), 1 ether);
        IERC20(******************************************).approve(address(router), 1 ether);

        cheats.expectRevert("MakerPsmExecutor: Invalid token");
        router.swap(
            ******************************************,
            1 ether,
            ******************************************,
            999e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }
}
