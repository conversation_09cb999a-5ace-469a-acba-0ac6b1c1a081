// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.25;

import "forge-std/Test.sol";
import "../src/router/Router.sol";
import "../src/executor/Executor.sol";
import "../src/aggregatorRouter/DexSwap.sol";
import "../src/library/SignedDecimalMath.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1000000 * 10**18);
    }
    
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

contract MockAdapter {
    using UniversalERC20 for IERC20;
    
    bool public shouldFail;
    uint256 public swapRatio = 10000; // 1:1 ratio by default
    
    function setShouldFail(bool _shouldFail) external {
        shouldFail = _shouldFail;
    }
    
    function setSwapRatio(uint256 _ratio) external {
        swapRatio = _ratio;
    }
    
    function executeSimpleSwap(
        address fromToken,
        address toToken,
        uint256 fromTokenAmount,
        Utils.SimpleSwap[] calldata swaps
    ) external {
        if (shouldFail) {
            revert("Mock adapter failure");
        }
        
        // Simple mock swap: transfer fromToken and mint toToken
        IERC20(fromToken).universalTransfer(payable(address(this)), fromTokenAmount);
        
        uint256 toTokenAmount = (fromTokenAmount * swapRatio) / 10000;
        MockERC20(toToken).mint(address(this), toTokenAmount);
        IERC20(toToken).universalTransfer(payable(msg.sender), toTokenAmount);
    }
}

contract InvariantTests is Test {
    using SignedDecimalMath for uint256;
    
    Router public router;
    Executor public executor;
    DexSwap public dexSwap;
    MockERC20 public tokenA;
    MockERC20 public tokenB;
    MockAdapter public adapter;
    
    address public admin1 = address(0x1);
    address public admin2 = address(0x2);
    address public admin3 = address(0x3);
    address public user = address(0x4);
    address public feeReceiver = address(0x5);
    
    uint256 public constant MAX_FEE_RATE = 1000; // 10%
    
    function setUp() public {
        // Deploy contracts
        address[3] memory admins = [admin1, admin2, admin3];
        router = new Router(admins, MAX_FEE_RATE);
        executor = router.executor();
        dexSwap = new DexSwap(admins, MAX_FEE_RATE);
        
        // Deploy tokens and adapter
        tokenA = new MockERC20("TokenA", "TKA");
        tokenB = new MockERC20("TokenB", "TKB");
        adapter = new MockAdapter();
        
        // Setup adapter in executor
        vm.prank(address(router));
        executor.updateAdaptor(address(adapter), true);
        
        // Mint tokens to user and adapter
        tokenA.mint(user, 1000 * 10**18);
        tokenB.mint(address(adapter), 1000 * 10**18);
        
        vm.deal(user, 100 ether);
    }
    
    /// @notice Invariant: Balance conservation during swaps
    function invariant_balanceConservation() public {
        uint256 totalSupplyBefore = tokenA.totalSupply() + tokenB.totalSupply();
        
        // Perform swap operations
        _performRandomSwap();
        
        uint256 totalSupplyAfter = tokenA.totalSupply() + tokenB.totalSupply();
        
        // Total supply should only increase (due to minting in mock adapter)
        assertGe(totalSupplyAfter, totalSupplyBefore);
    }
    
    /// @notice Invariant: Fee calculations are consistent
    function invariant_feeConsistency() public view {
        // Test fee calculation consistency
        uint256 amount = 1000 * 10**18;
        uint256 feeRate = 500; // 5%
        
        uint256 calculatedFee = amount.decimalMul(feeRate);
        uint256 expectedFee = (amount * feeRate) / SignedDecimalMath.ONE;
        
        assertEq(calculatedFee, expectedFee);
        assertLe(calculatedFee, amount); // Fee never exceeds principal
    }
    
    /// @notice Invariant: Admin pause state consistency
    function invariant_adminPauseConsistency() public view {
        uint256 actualPauseCount = 0;
        bool[3] memory pauseStates = router.getAdminPauseStates();
        
        for (uint256 i = 0; i < 3; i++) {
            if (pauseStates[i]) {
                actualPauseCount++;
            }
        }
        
        // Pause count should match actual paused admins
        assertEq(actualPauseCount, router.pauseCount());
        
        // If paused, at least one admin should be paused
        if (router.paused()) {
            assertGt(router.pauseCount(), 0);
        }
    }
    
    /// @notice Invariant: Percentage allocations sum to 100%
    function invariant_percentageSum() public {
        // Create valid multi-path with correct percentages
        Utils.MultiPath[] memory paths = _createValidPaths();
        
        uint256 totalPercent = 0;
        for (uint256 i = 0; i < paths.length; i++) {
            totalPercent += paths[i].percent;
        }
        
        assertEq(totalPercent, SignedDecimalMath.ONE);
    }
    
    /// @notice Invariant: Only whitelisted adapters can be used
    function invariant_adapterWhitelist() public view {
        assertTrue(executor.whiteListAdapter(address(adapter)));
        
        // Non-whitelisted adapter should return false
        assertFalse(executor.whiteListAdapter(address(0x999)));
    }
    
    /// @notice Test balance conservation with actual swap
    function testBalanceConservationWithSwap() public {
        uint256 amount = 100 * 10**18;
        uint256 feeRate = 100; // 1%
        
        vm.startPrank(user);
        tokenA.approve(address(router), amount);
        
        uint256 userTokenABefore = tokenA.balanceOf(user);
        uint256 userTokenBBefore = tokenB.balanceOf(user);
        uint256 feeReceiverBefore = tokenA.balanceOf(feeReceiver);
        
        Utils.MultiPath[] memory paths = _createValidPaths();
        
        router.swap(
            address(tokenA),
            amount,
            address(tokenB),
            0,
            true, // feeOnFromToken
            feeRate,
            feeReceiver,
            paths
        );
        
        uint256 userTokenAAfter = tokenA.balanceOf(user);
        uint256 userTokenBAfter = tokenB.balanceOf(user);
        uint256 feeReceiverAfter = tokenA.balanceOf(feeReceiver);
        
        vm.stopPrank();
        
        // Verify balance changes
        uint256 tokenASpent = userTokenABefore - userTokenAAfter;
        uint256 tokenBReceived = userTokenBAfter - userTokenBBefore;
        uint256 feeCollected = feeReceiverAfter - feeReceiverBefore;
        
        // User should have spent the input amount
        assertEq(tokenASpent, amount);
        
        // Fee should be collected correctly
        uint256 expectedFee = amount.decimalMul(feeRate);
        assertEq(feeCollected, expectedFee);
        
        // User should receive some output tokens
        assertGt(tokenBReceived, 0);
    }
    
    /// @notice Test admin pause/unpause consistency
    function testAdminPauseUnpauseConsistency() public {
        // Initially not paused
        assertFalse(router.paused());
        assertEq(router.pauseCount(), 0);
        
        // Admin1 pauses
        vm.prank(admin1);
        router.pause();
        
        assertTrue(router.paused());
        assertEq(router.pauseCount(), 1);
        
        // Admin2 also pauses
        vm.prank(admin2);
        router.pause();
        
        assertTrue(router.paused());
        assertEq(router.pauseCount(), 2);
        
        // Admin1 tries to unpause (should fail - multiple admins paused)
        vm.prank(admin1);
        vm.expectRevert("Admin: cannot unpause when multiple admins paused");
        router.unpause();
        
        // Admin2 unpauses
        vm.prank(admin2);
        router.unpause();
        
        assertTrue(router.paused()); // Still paused
        assertEq(router.pauseCount(), 1);
        
        // Admin1 unpauses
        vm.prank(admin1);
        router.unpause();
        
        assertFalse(router.paused()); // Now unpaused
        assertEq(router.pauseCount(), 0);
    }
    
    function _performRandomSwap() internal {
        if (tokenA.balanceOf(user) > 0) {
            uint256 amount = 1 * 10**18;
            
            vm.startPrank(user);
            tokenA.approve(address(router), amount);
            
            Utils.MultiPath[] memory paths = _createValidPaths();
            
            try router.swap(
                address(tokenA),
                amount,
                address(tokenB),
                0,
                false,
                0,
                feeReceiver,
                paths
            ) {} catch {}
            
            vm.stopPrank();
        }
    }
    
    function _createValidPaths() internal view returns (Utils.MultiPath[] memory) {
        Utils.MultiPath[] memory paths = new Utils.MultiPath[](1);
        
        paths[0].percent = SignedDecimalMath.ONE; // 100%
        paths[0].paths = new Utils.SinglePath[](1);
        paths[0].paths[0].toToken = address(tokenB);
        paths[0].paths[0].adapters = new Utils.Adapter[](1);
        paths[0].paths[0].adapters[0].adapter = payable(address(adapter));
        paths[0].paths[0].adapters[0].percent = SignedDecimalMath.ONE; // 100%
        paths[0].paths[0].adapters[0].swaps = new Utils.SimpleSwap[](1);
        paths[0].paths[0].adapters[0].swaps[0].percent = SignedDecimalMath.ONE; // 100%
        paths[0].paths[0].adapters[0].swaps[0].swapType = 1;
        paths[0].paths[0].adapters[0].swaps[0].data = "";
        
        return paths;
    }
}
