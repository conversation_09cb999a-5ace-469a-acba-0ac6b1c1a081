/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";

contract BalancerV2ExecutorTest is InitialTest {
    using SafeERC20 for IERC20;

    function testBalancerV2WethToCow() public {
        BalancerV2Param memory arg = BalancerV2Param(
            0xde8c195aa41c11a0c4787372defbbddaa31306d2000200000000000000000181,
            ******************************************
        );
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 8, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(******************************************, address(this), 5e17);
        IERC20(******************************************).forceApprove(address(router), 5e17);
        uint256 cowBalanceBefore = IERC20(******************************************).balanceOf(address(this));
        console.log("cowBalanceBefore: ", cowBalanceBefore);
        router.swap(
            ******************************************,
            5e17,
            ******************************************,
            2000e18,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 cowBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 ethBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        console.log("cowBalanceAfter: ", cowBalanceAfter);
        console.log("ethBalanceAfter: ", ethBalanceAfter);
        assertEq(ethBalanceAfter, 0);
    }
}
