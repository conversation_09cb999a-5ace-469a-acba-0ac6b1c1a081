/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "./DexSwapInitial.sol";
import {Test} from "forge-std/Test.sol";

contract KyberTest is DexSwapInitial {
    address public dai = ******************************************;
    address public weth = ******************************************;
    address public usdc = ******************************************;
    address public eth = ******************************************;

    event BalanceInfo(string message, uint256 fromBalanceChange, uint256 toBalanceChange, uint256 fee);

    function testDexSwapInKyberWETHTODAIChargeFeeOnWETH() public {
        deal(weth, address(this), 1 ether);
        IERC20(weth).approve(address(dexSwap), 1 ether);
        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));
        emit BalanceInfo("Before swap", wethBalanceBefore, daiBalanceBefore, 0);
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "kyber",
                fromToken: weth,
                fromTokenAmount: 1e18,
                toToken: dai,
                minAmountOut: 2000e18,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"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"
            })
        );
        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));
        uint256 fee = IERC20(weth).balanceOf(feeReceiver);

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, fee);
        assertTrue(daiBalanceAfter > daiBalanceBefore, "Should receive DAI");
        assertTrue(wethBalanceAfter < wethBalanceBefore, "Should spend WETH");
    }

    function testDexSwapInKyberEthToUsdcChargeFeeOnEth() public {
        vm.deal(address(this), 1 ether);
        uint256 ethBalanceBefore = address(this).balance;
        uint256 usdcBalanceBefore = IERC20(usdc).balanceOf(address(this));
        uint256 feeBefore = feeReceiver.balance;
        dexSwap.swap{value: 1 ether}(
            DexSwap.SwapParams({
                aggregatorId: "kyber",
                fromToken: eth,
                fromTokenAmount: 1e18,
                toToken: usdc,
                minAmountOut: 2000e6,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"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"
            })
        );
        uint256 ethBalanceAfter = address(this).balance;
        uint256 usdcBalanceAfter = IERC20(usdc).balanceOf(address(this));
        uint256 fee = feeReceiver.balance;
        emit BalanceInfo(
            "After swap", ethBalanceBefore - ethBalanceAfter, usdcBalanceAfter - usdcBalanceBefore, fee - feeBefore
        );
        assertTrue(usdcBalanceAfter > usdcBalanceBefore, "Should receive USDC");
        assertTrue(ethBalanceAfter < ethBalanceBefore, "Should spend ETH");
    }

    function testDexSwapInKyberWithRemainingTokens() public {
        deal(weth, address(this), 2 ether);
        IERC20(weth).approve(address(dexSwap), 2 ether);

        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));

        // Execute swap with 1 ether, leaving 1 ether in the adapter
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "kyber",
                fromToken: weth,
                fromTokenAmount: 1e18,
                toToken: dai,
                minAmountOut: 0,
                feeRate: 0,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"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"
            })
        );

        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, 0);
        // Check that we spent exactly 99e16 ether
        assertEq(wethBalanceBefore - wethBalanceAfter, 99e16, "Should spend exactly 99e16 ether");
    }

// emit a error invalid msg.value
    // function testDexSwapInKyberWithRemainingTokensETH() public {
    //     vm.deal(address(this), 2 ether);
    //     uint256 ethBalanceBefore = address(this).balance;
    //     uint256 usdcBalanceBefore = IERC20(usdc).balanceOf(address(this));
    //     dexSwap.swap{value: 2 ether}(
    //         DexSwap.SwapParams({
    //             aggregatorId: "kyber",
    //             fromToken: eth,
    //             fromTokenAmount: 2e18,
    //             toToken: usdc,
    //             minAmountOut: 2000e6,
    //             feeRate: 0,
    //             feeOnFromToken: true,
    //             feeReceiver: feeReceiver,
    //             data: hex"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"
    //         })
    //     );
    //     uint256 ethBalanceAfter = address(this).balance;
    //     uint256 usdcBalanceAfter = IERC20(usdc).balanceOf(address(this));
    //     emit BalanceInfo(
    //         "After swap", ethBalanceBefore - ethBalanceAfter, usdcBalanceAfter - usdcBalanceBefore, 0
    //     );

    //     assertEq(ethBalanceBefore - ethBalanceAfter, 99e16, "Should spend exactly 99e16 ether");
    //     assertTrue(usdcBalanceAfter > usdcBalanceBefore, "Should receive USDC");
    //     assertTrue(ethBalanceAfter < ethBalanceBefore, "Should spend ETH");
    // }
}
