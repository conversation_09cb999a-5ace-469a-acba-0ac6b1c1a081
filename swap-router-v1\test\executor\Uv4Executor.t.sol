/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";
/// @notice ETH mainnet test

contract Uv4ExecutorTest is InitialTest {
    using SafeERC20 for IERC20;

    function testFromEthToUSDCV4() public {
        // eth -> usdc 100%
        Utils.MultiPath memory multiPath0;
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        {
            Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
            PoolKey memory poolKey = PoolKey({
                currency0: Currency.wrap(******************************************),
                currency1: Currency.wrap(******************************************),
                fee: 3000,
                tickSpacing: 60,
                hooks: IHooks(******************************************)
            });
            UniswapV4Data memory arg = UniswapV4Data(******************************************, poolKey, "");
            bytes memory payload = abi.encode(arg);
            Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 12, payload);
            swaps[0] = simpleSwap;
            Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
            Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
            adapters[0] = adapter;
            Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
            singlePaths[0] = singlePath0;
        }
        multiPath0 = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath0;
        vm.deal(address(this), 1 ether);

        router.swap{value: 1 ether}(
            ******************************************,
            1 ether,
            ******************************************,
            1000e6,
            true,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 ethBalance = (address(this)).balance;
        console.logUint(ethBalance);
        uint256 usdcBalance = IERC20(******************************************).balanceOf(address(this));
        console.logUint(usdcBalance);
        assertTrue(usdcBalance > 1000e6);
    }

    function testFromUSDCToWethV4() public {
        // usdc -> weth 100%
        Utils.MultiPath memory multiPath0;
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](2);
        {
            Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
            PoolKey memory poolKey = PoolKey({
                currency0: Currency.wrap(******************************************),
                currency1: Currency.wrap(******************************************),
                fee: 500,
                tickSpacing: 10,
                hooks: IHooks(******************************************)
            });
            UniswapV4Data memory arg = UniswapV4Data(******************************************, poolKey, "");
            bytes memory payload = abi.encode(arg);
            Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 12, payload);
            swaps[0] = simpleSwap;
            Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
            Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
            adapters[0] = adapter;
            Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
            singlePaths[0] = singlePath0;
        }
        {
            Utils.SimpleSwap[] memory wethSwaps = new Utils.SimpleSwap[](1);
            Utils.SimpleSwap memory wethSimpleSwap = Utils.SimpleSwap(1e18, 11, "");
            wethSwaps[0] = wethSimpleSwap;
            Utils.Adapter[] memory wethAdapters = new Utils.Adapter[](1);
            Utils.Adapter memory wethAdapter = Utils.Adapter(payable(adapter1), 1e18, wethSwaps);
            wethAdapters[0] = wethAdapter;
            Utils.SinglePath memory wethSinglePath =
                Utils.SinglePath(******************************************, wethAdapters);
            singlePaths[1] = wethSinglePath;
        }
        multiPath0 = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath0;
        deal(******************************************, address(this), 2000e6);
        IERC20(******************************************).approve(address(router), 2000e6);

        router.swap(
            ******************************************,
            2000e6,
            ******************************************,
            5e17,
            true,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 wethBalance = IERC20(******************************************).balanceOf(address(this));
        assertTrue(wethBalance > 5e17);
    }

    function testFromUSDTToUSDCV4() public {
        // usdt -> usdc 100%
        Utils.MultiPath memory multiPath0;
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        {
            Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
            PoolKey memory poolKey = PoolKey({
                currency0: Currency.wrap(******************************************),
                currency1: Currency.wrap(******************************************),
                fee: 10,
                tickSpacing: 1,
                hooks: IHooks(******************************************)
            });
            UniswapV4Data memory arg = UniswapV4Data(******************************************, poolKey, "");
            bytes memory payload = abi.encode(arg);
            Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 12, payload);
            swaps[0] = simpleSwap;
            Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
            Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
            adapters[0] = adapter;
            Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
            singlePaths[0] = singlePath0;
        }
        multiPath0 = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath0;
        deal(******************************************, address(this), 2000e6);
        IERC20(******************************************).forceApprove(address(router), 2000e6);

        router.swap(
            ******************************************,
            2000e6,
            ******************************************,
            1999e6,
            true,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 usdcBalance = IERC20(******************************************).balanceOf(address(this));
        assertTrue(usdcBalance > 1999e6);
    }
}
