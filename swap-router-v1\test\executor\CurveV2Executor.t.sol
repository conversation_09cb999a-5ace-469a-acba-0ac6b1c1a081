/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";

contract MakerCurveV2ExecutorTest is InitialTest {
    using SafeERC20 for IERC20;

    function testCurveV2ExecutorFromWethToWbtc() public {
        CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 2, 1, address(0), 0, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(******************************************, address(this), 1e18);
        IERC20(******************************************).approve(address(router), 1 ether);
        router.swap(
            ******************************************,
            1 ether,
            ******************************************,
            150e4,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 wbtcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 wethBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        console.log("wbtcBalanceAfter: ", wbtcBalanceAfter);
        console.log("wethBalanceAfter: ", wethBalanceAfter);
        assertEq(wethBalanceAfter, 0);
        assertTrue(wbtcBalanceAfter > 150e4);
    }

    function testCurveV2ExecutorAddLiquidity() public {
        CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 2, 0, address(0), 4, 3);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(0xdAC17F958D2ee523a2206206994597C13D831ec7, address(this), 100e6);
        IERC20(0xdAC17F958D2ee523a2206206994597C13D831ec7).forceApprove(address(router), 100e6);
        router.swap(
            0xdAC17F958D2ee523a2206206994597C13D831ec7,
            100e6,
            ******************************************,
            0,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 curveFiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 usdtBalanceAfter = IERC20(0xdAC17F958D2ee523a2206206994597C13D831ec7).balanceOf(address(this));
        console.log("curveFiBalanceAfter: ", curveFiBalanceAfter);
        console.log("usdtBalanceAfter: ", usdtBalanceAfter);
    }

    function testCurveV2ExecutorRemoveLiquidity() public {
        CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 1, 0, address(0), 3, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(0xdf55670e27bE5cDE7228dD0A6849181891c9ebA1, address(this), 100e18);
        IERC20(0xdf55670e27bE5cDE7228dD0A6849181891c9ebA1).forceApprove(address(router), 100e18);
        router.swap(
            0xdf55670e27bE5cDE7228dD0A6849181891c9ebA1,
            100e18,
            ******************************************,
            0,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        // uint256 curveFiBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        // console.log("curveFiBalanceAfter: ", curveFiBalanceAfter);
        console.log("usdtBalanceAfter: ", usdcBalanceAfter);
    }

    function testCurveV2ExecutorFromETHToUSDTUnderlying() public {
        CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 2, 0, address(0), 1, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(******************************************, address(this), 10000 ether);
        IERC20(******************************************).forceApprove(address(router), 10000 ether);
        router.swap(
            ******************************************,
            10000 ether,
            ******************************************,
            0,
            false,
            0,
            feeReceiver,
            multiPaths
        );
        uint256 crvBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        uint256 crvUSDBalanceAfter = IERC20(******************************************).balanceOf(address(this));
        console.log("crvBalanceAfter: ", crvBalanceAfter);
        console.log("crvUSDBalanceAfter: ", crvUSDBalanceAfter);
        assertEq(crvBalanceAfter, 0);
    }

    function testCurveV2ExecutorWrongSwapType() public {
        CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 2, 0, address(0), 7, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;

        deal(******************************************, address(this), 10000 ether);
        IERC20(******************************************).forceApprove(address(router), 10000 ether);
        cheats.expectRevert("CurveV2Executor: Invalid swap type");
        router.swap(
            ******************************************,
            10000 ether,
            ******************************************,
            0,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }
}
