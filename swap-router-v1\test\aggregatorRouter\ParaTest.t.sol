/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "./DexSwapInitial.sol";
import {Test} from "forge-std/Test.sol";

contract ParaTest is DexSwapInitial {
    address public dai = ******************************************;
    address public weth = ******************************************;
    address public usdc = ******************************************;
    address public eth = ******************************************;

    event BalanceInfo(string message, uint256 fromBalanceChange, uint256 toBalanceChange, uint256 fee);

    function testDexSwapInParaWETHTODAIChargeFeeOnWETH() public {
        deal(weth, address(this), 1 ether);
        IERC20(weth).approve(address(dexSwap), 1 ether);
        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));
        emit BalanceInfo("Before swap", wethBalanceBefore, daiBalanceBefore, 0);
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "para",
                fromToken: weth,
                fromTokenAmount: 1e18,
                toToken: dai,
                minAmountOut: 2000e18,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"e3ead59e000000000000000000000000000010036c0190e009a000d0fc3541100a07380a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000006b175474e89094c44da98b954eedeac495271d0f0000000000000000000000000000000000000000000000000dbd2fc137a30000000000000000000000000000000000000000000000000081ef3cf7d79d39b1c50000000000000000000000000000000000000000000000833f3aff8475709489b3933f47867c485a9db6d74cf5fdb5bf000000000000000000000000015bcbe60000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000001600000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001c0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000001c0e592427a0aece92de3edee1f18e0157c0586156400000160008400000000000300000000000000000000000000000000000000000000000000000000c04b8d59000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000006a000f20005980200259b80c51020030400010680000000000000000000000000000000000000000000000000000000068674f170000000000000000000000000000000000000000000000000dbd2fc137a3000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000042c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000064dac17f958d2ee523a2206206994597c13d831ec70000646b175474e89094c44da98b954eedeac495271d0f000000000000000000000000000000000000000000000000000000000000"
            })
        );
        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));
        uint256 fee = IERC20(weth).balanceOf(feeReceiver);

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, fee);
        assertTrue(daiBalanceAfter > daiBalanceBefore, "Should receive DAI");
        assertTrue(wethBalanceAfter < wethBalanceBefore, "Should spend WETH");
    }

    function testDexSwapInParaEthToUsdcChargeFeeOnEth() public {
        vm.deal(address(this), 1 ether);
        uint256 ethBalanceBefore = address(this).balance;
        uint256 usdcBalanceBefore = IERC20(usdc).balanceOf(address(this));
        uint256 feeBefore = feeReceiver.balance;
        dexSwap.swap{value: 1 ether}(
            DexSwap.SwapParams({
                aggregatorId: "para",
                fromToken: eth,
                fromTokenAmount: 1e18,
                toToken: usdc,
                minAmountOut: 2000e6,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"e3ead59e000000000000000000000000000010036c0190e009a000d0fc3541100a07380a000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000000000000000000000000000000000000000000000000dbd2fc137a30000000000000000000000000000000000000000000000000000000000008f0b8c1f00000000000000000000000000000000000000000000000000000000907d71188d63ea3fff9c4a61bb8b9b32323f64a4000000000000000000000000015bcbfb0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000001600000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001e0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000001e0c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000002000040000ff00000900000000000000000000000000000000000000000000000000000000d0e30db0e592427a0aece92de3edee1f18e0157c0586156400000140008400000000000300000000000000000000000000000000000000000000000000000000c04b8d59000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000006a000f20005980200259b80c5102003040001068000000000000000000000000000000000000000000000000000000006867500f0000000000000000000000000000000000000000000000000dbd2fc137a300000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000002bc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000064a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000000000000000000000"
            })
        );
        uint256 ethBalanceAfter = address(this).balance;
        uint256 usdcBalanceAfter = IERC20(usdc).balanceOf(address(this));
        uint256 fee = feeReceiver.balance;
        emit BalanceInfo(
            "After swap", ethBalanceBefore - ethBalanceAfter, usdcBalanceAfter - usdcBalanceBefore, fee - feeBefore
        );
        assertTrue(usdcBalanceAfter > usdcBalanceBefore, "Should receive USDC");
        assertTrue(ethBalanceAfter < ethBalanceBefore, "Should spend ETH");
    }

    function testDexSwapInParaWithRemainingTokens() public {
        deal(weth, address(this), 2 ether);
        IERC20(weth).approve(address(dexSwap), 2 ether);
        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));

        // Execute swap with 1 ether, leaving 1 ether in the adapter
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "para",
                fromToken: weth,
                fromTokenAmount: 1e18,
                toToken: dai,
                minAmountOut: 0,
                feeRate: 0,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"e3ead59e000000000000000000000000000010036c0190e009a000d0fc3541100a07380a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000006b175474e89094c44da98b954eedeac495271d0f0000000000000000000000000000000000000000000000000dbd2fc137a30000000000000000000000000000000000000000000000000081ef3cf7d79d39b1c50000000000000000000000000000000000000000000000833f3aff8475709489b3933f47867c485a9db6d74cf5fdb5bf000000000000000000000000015bcbe60000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000001600000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001c0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000001c0e592427a0aece92de3edee1f18e0157c0586156400000160008400000000000300000000000000000000000000000000000000000000000000000000c04b8d59000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000006a000f20005980200259b80c51020030400010680000000000000000000000000000000000000000000000000000000068674f170000000000000000000000000000000000000000000000000dbd2fc137a3000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000042c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000064dac17f958d2ee523a2206206994597c13d831ec70000646b175474e89094c44da98b954eedeac495271d0f000000000000000000000000000000000000000000000000000000000000"
            })
        );

        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, 0);
        // Check that we spent exactly 99e16 ether
        assertEq(wethBalanceBefore - wethBalanceAfter, 99e16, "Should spend exactly 99e16 ether");
    }
}
