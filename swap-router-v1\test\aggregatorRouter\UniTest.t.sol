/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "./DexSwapInitial.sol";
import {Test} from "forge-std/Test.sol";

contract UniTest is DexSwapInitial {
    address public dai = ******************************************;
    address public weth = ******************************************;
    address public usdc = ******************************************;
    address public eth = ******************************************;

    event BalanceInfo(string message, uint256 fromBalanceChange, uint256 toBalanceChange, uint256 fee);

    function testDexSwapInUniWETHTODAIChargeFeeOnWETH() public {
        deal(weth, address(this), 1 ether);
        IERC20(weth).approve(address(dexSwap), 1 ether);
        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));
        emit BalanceInfo("Before swap", wethBalanceBefore, daiBalanceBefore, 0);
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "uni",
                fromToken: weth,
                fromTokenAmount: 1e18,
                toToken: dai,
                minAmountOut: 2000e18,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006863654c00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000120000000000000000000000000104fbc016f4bb334d775a19e8a6510109ac63e000000000000000000000000000000000000000000000000000dbd2fc137a30000000000000000000000000000000000000000000000000083fe78d20a04ca6c5100000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000042c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20001f4a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000646b175474e89094c44da98b954eedeac495271d0f0000000000000000000000000000000000000000000000000000000000001b"
            })
        );
        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));
        uint256 fee = IERC20(weth).balanceOf(feeReceiver);

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, fee);
        assertTrue(daiBalanceAfter > daiBalanceBefore, "Should receive DAI");
        assertTrue(wethBalanceAfter < wethBalanceBefore, "Should spend WETH");
    }

    function testDexSwapInUniEthToUsdcChargeFeeOnEth() public {
        vm.deal(address(this), 1 ether);
        uint256 ethBalanceBefore = address(this).balance;
        uint256 usdcBalanceBefore = IERC20(usdc).balanceOf(address(this));
        uint256 feeBefore = feeReceiver.balance;
        dexSwap.swap{value: 1 ether}(
            DexSwap.SwapParams({
                aggregatorId: "uni",
                fromToken: eth,
                fromTokenAmount: 1e18,
                toToken: usdc,
                minAmountOut: 2000e6,
                feeRate: 1e16,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006863657900000000000000000000000000000000000000000000000000000000000000020b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000dbd2fc137a300000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000104fbc016f4bb334d775a19e8a6510109ac63e000000000000000000000000000000000000000000000000000dbd2fc137a300000000000000000000000000000000000000000000000000000000000091230bda00000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002bc02aaa39b223fe8d0a0e5c4f27ead9083c756cc20001f4a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000000000000000000000000000000000000000001b"
            })
        );
        uint256 ethBalanceAfter = address(this).balance;
        uint256 usdcBalanceAfter = IERC20(usdc).balanceOf(address(this));
        uint256 fee = feeReceiver.balance;
        emit BalanceInfo(
            "After swap", ethBalanceBefore - ethBalanceAfter, usdcBalanceAfter - usdcBalanceBefore, fee - feeBefore
        );
        assertTrue(usdcBalanceAfter > usdcBalanceBefore, "Should receive USDC");
        assertTrue(ethBalanceAfter < ethBalanceBefore, "Should spend ETH");
    }

    function testDexSwapInUniWithRemainingTokens() public {
        deal(weth, address(this), 2 ether);
        IERC20(weth).approve(address(dexSwap), 2 ether);
        uint256 wethBalanceBefore = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceBefore = IERC20(dai).balanceOf(address(this));

        // Execute swap with 1 ether, leaving 1 ether in the adapter
        dexSwap.swap(
            DexSwap.SwapParams({
                aggregatorId: "uni",
                fromToken: weth,
                fromTokenAmount: 2e18,
                toToken: dai,
                minAmountOut: 0,
                feeRate: 0,
                feeOnFromToken: true,
                feeReceiver: feeReceiver,
                data: hex"3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006863654c00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000120000000000000000000000000104fbc016f4bb334d775a19e8a6510109ac63e000000000000000000000000000000000000000000000000000dbd2fc137a30000000000000000000000000000000000000000000000000083fe78d20a04ca6c5100000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000042c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20001f4a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000646b175474e89094c44da98b954eedeac495271d0f0000000000000000000000000000000000000000000000000000000000001b"
            })
        );

        uint256 wethBalanceAfter = IERC20(weth).balanceOf(address(this));
        uint256 daiBalanceAfter = IERC20(dai).balanceOf(address(this));

        emit BalanceInfo("After swap", wethBalanceBefore - wethBalanceAfter, daiBalanceAfter - daiBalanceBefore, 0);
        // Check that we spent exactly 99e16 ether
        assertEq(wethBalanceBefore - wethBalanceAfter, 99e16, "Should spend exactly 99e16 ether");
    }
    
    // remain eth will not transfer back
    // function testDexSwapInUniWithRemainingTokensETH() public {
    //     vm.deal(address(this), 2 ether);
    //     uint256 ethBalanceBefore = address(this).balance;
    //     uint256 usdcBalanceBefore = IERC20(usdc).balanceOf(address(this));
    //     dexSwap.swap{value: 2 ether}(
    //         DexSwap.SwapParams({
    //             aggregatorId: "uni",
    //             fromToken: eth,
    //             fromTokenAmount: 2e18,
    //             toToken: usdc,
    //             minAmountOut: 2000e6,
    //             feeRate: 0,
    //             feeOnFromToken: true,
    //             feeReceiver: feeReceiver,
    //             data: hex"3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000684ee3c100000000000000000000000000000000000000000000000000000000000000020b000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000dbd2fc137a300000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000104fbc016f4bb334d775a19e8a6510109ac63e000000000000000000000000000000000000000000000000000dbd2fc137a3000000000000000000000000000000000000000000000000000000000000948087a400000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002bc02aaa39b223fe8d0a0e5c4f27ead9083c756cc20001f4a0b86991c6218b36c1d19d4a2e9eb0ce3606eb480000000000000000000000000000000000000000001b"
    //         })
    //     );
    //     uint256 ethBalanceAfter = address(this).balance;
    //     uint256 usdcBalanceAfter = IERC20(usdc).balanceOf(address(this));
    //     emit BalanceInfo(
    //         "After swap", ethBalanceBefore - ethBalanceAfter, usdcBalanceAfter - usdcBalanceBefore, 0
    //     );

    //     assertEq(ethBalanceBefore - ethBalanceAfter, 99e16, "Should spend exactly 99e16 ether");
    //     assertTrue(usdcBalanceAfter > usdcBalanceBefore, "Should receive USDC");
    //     assertTrue(ethBalanceAfter < ethBalanceBefore, "Should spend ETH");
    // }
}
