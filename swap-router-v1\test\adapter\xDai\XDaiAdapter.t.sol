// /*
//     Copyright Debank
//     SPDX-License-Identifier: BUSL-1.1
// */
// pragma solidity ^0.8.25;

// import "./XDaiInitial.t.sol";

// contract XDaiAdapterTest is XDaiInitialTest {
//     function testSushiSwapV2WethToBtc() public {
//         uint256 btcBeforeBalance = IERC20(******************************************).balanceOf(address(this));
//         UniswapV2SwapArg memory arg = UniswapV2SwapArg(******************************************, 3, 1000);
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 1, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;
//         deal(******************************************, address(this), 1e18);
//         IERC20(******************************************).approve(address(router), 1e18);

//         router.swap(
//             ******************************************,
//             1 ether,
//             ******************************************,
//             1e6,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 btcBalance = IERC20(******************************************).balanceOf(address(this));
//         console.log(btcBalance - btcBeforeBalance);
//     }

//     function testSwaprV2WethToWxdai() public {
//         UniswapV2SwapArg memory arg = UniswapV2SwapArg(******************************************, 3, 1000);
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 1, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;
//         deal(******************************************, address(this), 1e18);
//         IERC20(******************************************).approve(address(router), 1e18);

//         router.swap(
//             // weth
//             ******************************************,
//             1 ether,
//             // wxdai
//             ******************************************,
//             1300e18,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 wxdaiBalance = IERC20(******************************************).balanceOf(address(this));
//         console.log(wxdaiBalance);
//     }

//     function testXDaiCurveV1ExecutorFromDAIToUSDC() public {
//         CurveV1SwapArg memory arg = CurveV1SwapArg(******************************************, 0, 1, 0);
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 3, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;
//         deal(******************************************, address(this), 100e18);
//         IERC20(******************************************).approve(address(router), 100 ether);

//         uint256 usdcBalanceBefore = IERC20(******************************************).balanceOf(address(this));
//         router.swap(
//             // xdai
//             ******************************************,
//             100 ether,
//             // usdc
//             ******************************************,
//             99e6,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 usdcBalanceAfter = IERC20(******************************************).balanceOf(address(this));
//         console.log("usdcBalanceAfter: ", usdcBalanceAfter - usdcBalanceBefore);
//     }

//     function testXDaiCurveV2ExecutorFromCrvToEur() public {
//         CurveV2SwapArg memory arg = CurveV2SwapArg(******************************************, 1, 0, address(0), 0, 0);
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 4, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;
//         deal(******************************************, address(this), 100e18);
//         IERC20(******************************************).approve(address(router), 100 ether);

//         uint256 eurBalanceBefore = IERC20(******************************************).balanceOf(address(this));
//         router.swap(
//             // crv
//             ******************************************,
//             100 ether,
//             // eur
//             ******************************************,
//             50e18,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 eurBalanceAfter = IERC20(******************************************).balanceOf(address(this));
//         console.log("eurBalanceAfter: ", eurBalanceAfter - eurBalanceBefore);
//     }

//     function testXDaiBalancerV2SdaiToWsteth() public {
//         BalancerV2Param memory arg = BalancerV2Param(
//             // poolId
//             0xbc2acf5e821c5c9f8667a36bb1131dad26ed64f9000200000000000000000063,
//             // pool
//             ******************************************
//         );
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 8, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;

//         deal(******************************************, address(this), 1000e18);
//         IERC20(******************************************).approve(address(router), 1000e18);
//         uint256 wstEthBalanceBefore = IERC20(******************************************).balanceOf(address(this));
//         console.log("wstEthBalanceBefore: ", wstEthBalanceBefore);
//         router.swap(
//             ******************************************,
//             1000e18,
//             ******************************************,
//             2e17,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 wstEthBalanceAfter = IERC20(******************************************).balanceOf(address(this));
//         console.log("wstEthBalanceAfter: ", wstEthBalanceAfter - wstEthBalanceBefore);
//     }

//     function testSwaprV3WethToWxdai() public {
//         AlgebraV3Executor.AlgebraV3Data memory arg = AlgebraV3Executor.AlgebraV3Data(
//             // router
//             ******************************************,
//             0
//         );
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);
//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 9, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath0;
//         Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath;
//         deal(******************************************, address(this), 1e18);
//         IERC20(******************************************).approve(address(router), 1e18);

//         router.swap(
//             // weth
//             ******************************************,
//             1 ether,
//             // wxdai
//             ******************************************,
//             1500e18,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         uint256 wxdaiBalance = IERC20(******************************************).balanceOf(address(this));
//         console.log(wxdaiBalance);
//     }

//     function testXDaiSushiV3UsdcToWxdai() public {
//         Utils.UniswapV3Data memory arg = Utils.UniswapV3Data(
//             // router
//             ******************************************,
//             0,
//             100
//         );
//         Utils.MultiPath memory multiPath0;
//         Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
//         bytes memory payload = abi.encode(arg);

//         Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 10, payload);
//         swaps[0] = simpleSwap;
//         Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
//         Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
//         adapters[0] = adapter;
//         Utils.SinglePath memory singlePath = Utils.SinglePath(
//             // toToken
//             ******************************************,
//             adapters
//         );
//         Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
//         singlePaths[0] = singlePath;
//         multiPath0 = Utils.MultiPath(1e18, singlePaths);

//         Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
//         multiPaths[0] = multiPath0;
//         deal(******************************************, address(this), 100e6);
//         IERC20(******************************************).approve(address(router), 100e6);

//         router.swap(
//             ******************************************,
//             100e6,
//             ******************************************,
//             80e18,
//             false,
//             0,
//             feeReceiver,
//             multiPaths
//         );
//         //     uint256 wxDaiBalance = IERC20(******************************************).balanceOf(address(this));
//         //     console.log(wxDaiBalance);
//         //     assertTrue(wxDaiBalance > 0, "wxDaiBalance should be greater than 0");
//     }
// }
