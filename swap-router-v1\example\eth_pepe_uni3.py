from web3 import Web3
import eth_abi
import json

with open("Router.json") as f:
    info_json = json.load(f)

router_abi = info_json["abi"]
router_address = "******************************************"

rpc = "https://mainnet.infura.io/v3/********************************"

web3 = Web3(Web3.HTTPProvider(rpc))

router = web3.eth.contract(address=router_address, abi=router_abi)

executor = "******************************************"

adapter = "******************************************"

### 换 1eth 到 pepe
# ****************************************** 代表eth
from_token = "******************************************" 

# pepe地址
to_token = "******************************************"
# 最小获得的pepe数量
min_amount_out = 10
# 交易手续费 n/1e18
fee_rate = 0  
# 交易手续费接收地址, 按 to token 收取
fee_receiver = "******************************************"

# 交易路径

e18 = 1000000000000000000
# path0 eth -> pepe , uniswap v3 pool
eth_pepe_swaparg = {
    'router' : "******************************************",
    'sqrtX96' : 0, 
    'fee' : 3000,
}
data = eth_abi.abi.encode(('address', 'uint160', 'uint24'), [eth_pepe_swaparg['router'], eth_pepe_swaparg['sqrtX96'], eth_pepe_swaparg['fee']])

simple_swap = (e18,5,data) # e18 是percent,n/1e18, 5 是swapType, data 是data

adapter = (adapter,e18,[simple_swap])

single_path = ("******************************************",[adapter]) # ****************************************** 是toToken地址,

multi_path = (e18,[single_path])

result = router.functions.swap(
    executor,
    from_token,
    e18,
    to_token,
    min_amount_out,
    True,
    fee_rate,
    fee_receiver,
    [multi_path]
).estimate_gas({'from': '******************************************','value':e18 })

print(result)