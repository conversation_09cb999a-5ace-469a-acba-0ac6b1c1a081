{"abi": [{"type": "constructor", "inputs": [{"name": "_routerOwner", "type": "address", "internalType": "address"}, {"name": "_weth", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "WETH", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "addAdaptor", "inputs": [{"name": "_executor", "type": "address", "internalType": "address"}, {"name": "_adapter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addExecutor", "inputs": [{"name": "executor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isReceiver<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "maxFeeRate", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pauseExecutor", "inputs": [{"name": "_executor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "removeExecutor", "inputs": [{"name": "executor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMaxFeeRate", "inputs": [{"name": "_maxFeeRate", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "superWithdraw", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "swap", "inputs": [{"name": "executor", "type": "address", "internalType": "address"}, {"name": "fromToken", "type": "address", "internalType": "address"}, {"name": "fromTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "toToken", "type": "address", "internalType": "address"}, {"name": "minAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "feeOnFromToken", "type": "bool", "internalType": "bool"}, {"name": "feeRate", "type": "uint256", "internalType": "uint256"}, {"name": "feeReceiver", "type": "address", "internalType": "address"}, {"name": "paths", "type": "tuple[]", "internalType": "struct Utils.MultiPath[]", "components": [{"name": "percent", "type": "uint256", "internalType": "uint256"}, {"name": "paths", "type": "tuple[]", "internalType": "struct Utils.SinglePath[]", "components": [{"name": "toToken", "type": "address", "internalType": "address"}, {"name": "adapters", "type": "tuple[]", "internalType": "struct Utils.Adapter[]", "components": [{"name": "adapter", "type": "address", "internalType": "address payable"}, {"name": "percent", "type": "uint256", "internalType": "uint256"}, {"name": "swaps", "type": "tuple[]", "internalType": "struct Utils.SimpleSwap[]", "components": [{"name": "percent", "type": "uint256", "internalType": "uint256"}, {"name": "swapType", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}]}]}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpauseExecutor", "inputs": [{"name": "_executor", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateFeeReceiver", "inputs": [{"name": "feeReceiver", "type": "address", "internalType": "address"}, {"name": "add", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "ExecutorUpdate", "inputs": [{"name": "executor", "type": "address", "indexed": false, "internalType": "address"}, {"name": "add", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "fromToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "fromTokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "toToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "toTokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "fee", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "503:6669:33:-:0;;;1148:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1205:12;-1:-1:-1;;;;;1273:26:24;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:24;;1350:1;1322:31;;;640:51:66;613:18;;1322:31:24;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;1241:5:30;1231:15;;-1:-1:-1;;;;1231:15:30;;;1229:4:33::1;:12:::0;;-1:-1:-1;;;;;;1229:12:33::1;-1:-1:-1::0;;;;;1229:12:33;;;::::1;::::0;;;::::1;::::0;;-1:-1:-1;503:6669:33;;2912:187:24;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:24;;;-1:-1:-1;;;;;;3020:17:24;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:177:66:-;93:13;;-1:-1:-1;;;;;135:31:66;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:293::-;275:6;283;336:2;324:9;315:7;311:23;307:32;304:52;;;352:1;349;342:12;304:52;375:40;405:9;375:40;:::i;:::-;365:50;;434:49;479:2;468:9;464:18;434:49;:::i;:::-;424:59;;196:293;;;;;:::o;494:203::-;503:6669:33;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "503:6669:33:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6849:118;;;;;;;;;;-1:-1:-1;6849:118:33;;;;;:::i;:::-;;:::i;:::-;;5951:156;;;;;;;;;;-1:-1:-1;5951:156:33;;;;;:::i;:::-;;:::i;6356:161::-;;;;;;;;;;-1:-1:-1;6356:161:33;;;;;:::i;:::-;;:::i;5329:406::-;;;;;;;;;;-1:-1:-1;5329:406:33;;;;;:::i;:::-;;:::i;713:51::-;;;;;;;;;;-1:-1:-1;713:51:33;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;960:14:66;;953:22;935:41;;923:2;908:18;713:51:33;;;;;;;;1844:2293;;;;;;:::i;:::-;;:::i;6973:134::-;;;;;;;;;;-1:-1:-1;6973:134:33;;;;;:::i;:::-;;:::i;6660:63::-;;;;;;;;;;;;;:::i;1850:84:30:-;;;;;;;;;;-1:-1:-1;1897:4:30;1920:7;-1:-1:-1;;;1920:7:30;;;;1850:84;;2293:101:24;;;;;;;;;;;;;:::i;6558:59:33:-;;;;;;;;;;;;;:::i;657:25::-;;;;;;;;;;;;;;;;;;;3125::66;;;3113:2;3098:18;657:25:33;2979:177:66;5741:104:33;;;;;;;;;;-1:-1:-1;5741:104:33;;;;;:::i;:::-;;:::i;1638:85:24:-;;;;;;;;;;-1:-1:-1;1684:7:24;1710:6;-1:-1:-1;;;;;1710:6:24;1638:85;;;-1:-1:-1;;;;;3556:32:66;;;3538:51;;3526:2;3511:18;1638:85:24;3392:203:66;6113:132:33;;;;;;;;;;-1:-1:-1;6113:132:33;;;;;:::i;:::-;;:::i;688:19::-;;;;;;;;;;-1:-1:-1;688:19:33;;;;-1:-1:-1;;;;;688:19:33;;;770:51;;;;;;;;;;-1:-1:-1;770:51:33;;;;;:::i;:::-;;;;;;;;;;;;;;;;2543:215:24;;;;;;;;;;-1:-1:-1;2543:215:24;;;;;:::i;:::-;;:::i;6729:114:33:-;;;;;;;;;;-1:-1:-1;6729:114:33;;;;;:::i;:::-;;:::i;6849:118::-;1531:13:24;:11;:13::i;:::-;6932:9:33::1;-1:-1:-1::0;;;;;6922:36:33::1;;:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;6849:118:::0;:::o;5951:156::-;1531:13:24;:11;:13::i;:::-;-1:-1:-1;;;;;6019:29:33;::::1;;::::0;;;:19:::1;:29;::::0;;;;;;;;:36;;-1:-1:-1;;6019:36:33::1;6051:4;6019:36:::0;;::::1;::::0;;;6070:30;;4155:51:66;;;4222:18;;;4215:50;6070:30:33::1;::::0;4128:18:66;6070:30:33::1;;;;;;;;5951:156:::0;:::o;6356:161::-;1531:13:24;:11;:13::i;:::-;-1:-1:-1;;;;;6427:29:33;::::1;6459:5;6427:29:::0;;;:19:::1;:29;::::0;;;;;;;:37;;-1:-1:-1;;6427:37:33::1;::::0;;6479:31;;4155:51:66;;;4222:18;;;4215:50;;;;6479:31:33::1;::::0;4128:18:66;6479:31:33::1;3987:284:66::0;5329:406:33;1531:13:24;:11;:13::i;:::-;-1:-1:-1;;;;;5412:36:33;::::1;280:42:56;5412:36:33;5408:321;;5485:38;::::0;-1:-1:-1;;;5485:38:33;;5517:4:::1;5485:38;::::0;::::1;3538:51:66::0;5464:18:33::1;::::0;-1:-1:-1;;;;;5485:23:33;::::1;::::0;::::1;::::0;3511:18:66;;5485:38:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5464:59:::0;-1:-1:-1;5537:42:33::1;-1:-1:-1::0;;;;;5537:26:33;::::1;5564:2:::0;5464:59;5537:26:::1;:42::i;:::-;5450:140;5329:406:::0;;:::o;5408:321::-:1;5666:52;::::0;-1:-1:-1;;;5666:52:33;;-1:-1:-1;;;;;4665:32:66;;5666:52:33::1;::::0;::::1;4647:51:66::0;5631:21:33::1;4714:18:66::0;;;4707:34;;;5631:21:33;280:42:56::1;::::0;5666:27:33::1;::::0;4620:18:66;;5666:52:33::1;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;5596:133;5329:406:::0;;:::o;1844:2293::-;1474:19:30;:17;:19::i;:::-;-1:-1:-1;;;;;2179:29:33;::::1;;::::0;;;:19:::1;:29;::::0;;;;;::::1;;2171:77;;;::::0;-1:-1:-1;;;2171:77:33;;5204:2:66;2171:77:33::1;::::0;::::1;5186:21:66::0;5243:2;5223:18;;;5216:30;5282:34;5262:18;;;5255:62;-1:-1:-1;;;5333:18:66;;;5326:33;5376:19;;2171:77:33::1;;;;;;;;;-1:-1:-1::0;;;;;2266:32:33;::::1;;::::0;;;:19:::1;:32;::::0;;;;;::::1;;2258:84;;;::::0;-1:-1:-1;;;2258:84:33;;5608:2:66;2258:84:33::1;::::0;::::1;5590:21:66::0;5647:2;5627:18;;;5620:30;5686:34;5666:18;;;5659:62;-1:-1:-1;;;5737:18:66;;;5730:37;5784:19;;2258:84:33::1;5406:403:66::0;2258:84:33::1;2371:10;;2360:7;:21;;2352:61;;;::::0;-1:-1:-1;;;2352:61:33;;6016:2:66;2352:61:33::1;::::0;::::1;5998:21:66::0;6055:2;6035:18;;;6028:30;6094:29;6074:18;;;6067:57;6141:18;;2352:61:33::1;5814:351:66::0;2352:61:33::1;-1:-1:-1::0;;;;;2458:40:33;::::1;280:42:56;2458:40:33;:62;;2519:1;2458:62;;;2501:15;2458:62;2444:9;:77;2423:139;;;::::0;-1:-1:-1;;;2423:139:33;;6372:2:66;2423:139:33::1;::::0;::::1;6354:21:66::0;6411:2;6391:18;;;6384:30;6450:29;6430:18;;;6423:57;6497:18;;2423:139:33::1;6170:351:66::0;2423:139:33::1;2572:17;2628:14;2624:138;;;2676:75;2686:9;2697:14;2713:15;2730:7;2739:11;2676:9;:75::i;:::-;2658:93;;2624:138;2837:46;2846:9;2857:15;2874:8;2837;:46::i;:::-;2894:21;2949:7:::0;-1:-1:-1;;;;;2970:38:33;::::1;280:42:56;2970:38:33;2966:237;;3040:40;::::0;-1:-1:-1;;;3040:40:33;;3074:4:::1;3040:40;::::0;::::1;3538:51:66::0;-1:-1:-1;;;;;3040:25:33;::::1;::::0;::::1;::::0;3511:18:66;;3040:40:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3024:56;;2966:237;;;3134:4;::::0;3127:37:::1;::::0;-1:-1:-1;;;3127:37:33;;3158:4:::1;3127:37;::::0;::::1;3538:51:66::0;-1:-1:-1;;;;;3134:4:33;;::::1;::::0;3127:22:::1;::::0;3511:18:66;;3127:37:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3188:4;::::0;-1:-1:-1;;;;;3188:4:33::1;::::0;-1:-1:-1;3111:53:33;-1:-1:-1;2966:237:33::1;3272:8;-1:-1:-1::0;;;;;3262:35:33::1;;280:42:56;-1:-1:-1::0;;;;;3298:40:33::1;:9;-1:-1:-1::0;;;;;3298:40:33::1;;:59;;3348:9;3298:59;;;3341:4;::::0;-1:-1:-1;;;;;3341:4:33::1;3298:59;3359:7;3368:5;;3262:112;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;-1:-1:-1::0;;3410:40:33::1;::::0;-1:-1:-1;;;3410:40:33;;3444:4:::1;3410:40;::::0;::::1;3538:51:66::0;3385:22:33::1;::::0;-1:-1:-1;3453:13:33;;-1:-1:-1;;;;;;3410:25:33;::::1;::::0;::::1;::::0;3511:18:66;;3410:40:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:56;;;;:::i;:::-;3385:81;;3506:14;3501:135;;3553:72;3563:7;3572:14;3588;3604:7;3613:11;3553:9;:72::i;:::-;3536:89;;3501:135;3695:12;3677:14;:30;;3669:74;;;::::0;-1:-1:-1;;;3669:74:33;;13584:2:66;3669:74:33::1;::::0;::::1;13566:21:66::0;13623:2;13603:18;;;13596:30;13662:33;13642:18;;;13635:61;13713:18;;3669:74:33::1;13382:355:66::0;3669:74:33::1;-1:-1:-1::0;;;;;;;3782:44:33;::::1;::::0;3778:258:::1;;3848:4;::::0;3842:36:::1;::::0;-1:-1:-1;;;3842:36:33;;::::1;::::0;::::1;3125:25:66::0;;;-1:-1:-1;;;;;3848:4:33;;::::1;::::0;3842:20:::1;::::0;3098:18:66;;3842:36:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;-1:-1:-1::0;;3892:46:33::1;::::0;3901:10:::1;::::0;-1:-1:-1;3892:46:33;::::1;;;::::0;-1:-1:-1;3923:14:33;;3892:46:::1;::::0;;;3923:14;3901:10;3892:46;::::1;;;;;;;;;;;;;::::0;::::1;;;;;;3778:258;;;3969:56;-1:-1:-1::0;;;;;3969:28:33;::::1;3998:10;4010:14:::0;3969:28:::1;:56::i;:::-;4050:80;4055:10;4067:9;4078:15;4095:7;4104:14;4120:9;4050:80;;;;;;;;;;-1:-1:-1::0;;;;;14047:32:66;;;14029:51;;14116:32;;;14111:2;14096:18;;14089:60;14180:2;14165:18;;14158:34;;;;14228:32;;14223:2;14208:18;;14201:60;14292:3;14277:19;;14270:35;;;;14067:3;14321:19;;14314:35;;;;14016:3;14001:19;;13742:613;4050:80:33::1;;;;;;;;2161:1976;;;;1844:2293:::0;;;;;;;;;;:::o;6973:134::-;1531:13:24;:11;:13::i;:::-;7059:41:33::1;::::0;-1:-1:-1;;;7059:41:33;;-1:-1:-1;;;;;3556:32:66;;;7059:41:33::1;::::0;::::1;3538:51:66::0;7059:31:33;::::1;::::0;::::1;::::0;3511:18:66;;7059:41:33::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;6973:134:::0;;:::o;6660:63::-;1531:13:24;:11;:13::i;:::-;6706:10:33::1;:8;:10::i;:::-;6660:63::o:0;2293:101:24:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;6558:59:33:-:0;1531:13:24;:11;:13::i;:::-;6602:8:33::1;:6;:8::i;5741:104::-:0;1531:13:24;:11;:13::i;:::-;5814:10:33::1;:24:::0;5741:104::o;6113:132::-;1531:13:24;:11;:13::i;:::-;-1:-1:-1;;;;;6200:32:33;;;::::1;;::::0;;;:19:::1;:32;::::0;;;;:38;;-1:-1:-1;;6200:38:33::1;::::0;::::1;;::::0;;;::::1;::::0;;6113:132::o;2543:215:24:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:24;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:24;;2700:1:::1;2672:31;::::0;::::1;3538:51:66::0;3511:18;;2672:31:24::1;3392:203:66::0;2623:91:24::1;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;6729:114:33:-;1531:13:24;:11;:13::i;:::-;6810:9:33::1;-1:-1:-1::0;;;;;6800:34:33::1;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;1796:162:24::0;1684:7;1710:6;-1:-1:-1;;;;;1710:6:24;735:10:29;1855:23:24;1851:101;;1901:40;;-1:-1:-1;;;1901:40:24;;735:10:29;1901:40:24;;;3538:51:66;3511:18;;1901:40:24;3392:203:66;1303:160:27;1412:43;;-1:-1:-1;;;;;4665:32:66;;;1412:43:27;;;4647:51:66;4714:18;;;4707:34;;;1385:71:27;;1405:5;;1427:14;;;;;4620:18:66;;1412:43:27;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1412:43:27;;;;;;;;;;;1385:19;:71::i;2002:128:30:-;1897:4;1920:7;-1:-1:-1;;;1920:7:30;;;;2063:61;;;2098:15;;-1:-1:-1;;;2098:15:30;;;;;;;;;;;4143:725:33;4286:7;;4329:26;:6;4347:7;4329:17;:26::i;:::-;4309:46;-1:-1:-1;4369:11:33;;4365:461;;4400:14;4396:420;;;4434:39;4443:5;4450:9;4461:11;4434:8;:39::i;:::-;4396:420;;;-1:-1:-1;;;;;;;4516:36:33;;;4512:290;;4582:4;;;;;;;;;-1:-1:-1;;;;;4582:4:33;-1:-1:-1;;;;;4576:19:33;;4603:9;4576:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4644:4:33;;4637:49;;-1:-1:-1;;;;;;4644:4:33;;-1:-1:-1;4663:11:33;;-1:-1:-1;4676:9:33;4637:25;:49::i;4512:290::-;4733:50;-1:-1:-1;;;;;4733:26:33;;4760:11;4773:9;4733:26;:50::i;:::-;4842:19;4852:9;4842:19;;:::i;:::-;;4143:725;-1:-1:-1;;;;;;;4143:725:33:o;4874:373::-;-1:-1:-1;;;;;;;4971:40:33;;;4967:274;;5033:4;;;;;;;;;-1:-1:-1;;;;;5033:4:33;-1:-1:-1;;;;;5027:19:33;;5054:15;5027:45;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5093:4:33;;5086:46;;-1:-1:-1;;;;;;5093:4:33;;-1:-1:-1;5112:2:33;;-1:-1:-1;5116:15:33;5086:25;:46::i;4967:274::-;5163:67;-1:-1:-1;;;;;5163:34:33;;5198:10;5210:2;5214:15;5163:34;:67::i;2710:117:30:-;1721:16;:14;:16::i;:::-;2778:5:::1;2768:15:::0;;-1:-1:-1;;;;2768:15:30::1;::::0;;2798:22:::1;735:10:29::0;2807:12:30::1;2798:22;::::0;-1:-1:-1;;;;;3556:32:66;;;3538:51;;3526:2;3511:18;2798:22:30::1;;;;;;;2710:117::o:0;2912:187:24:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:24;;;-1:-1:-1;;;;;;3020:17:24;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;2463:115:30:-;1474:19;:17;:19::i;:::-;2522:7:::1;:14:::0;;-1:-1:-1;;;;2522:14:30::1;-1:-1:-1::0;;;2522:14:30::1;::::0;;2551:20:::1;2558:12;735:10:29::0;;656:96;4059:629:27;4478:23;4504:33;-1:-1:-1;;;;;4504:27:27;;4532:4;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:27;;-1:-1:-1;;;;;3556:32:66;;4631:40:27;;;3538:51:66;3511:18;;4631:40:27;3392:203:66;610:111:53;675:7;238:4;702:5;706:1;702;:5;:::i;:::-;701:13;;;;:::i;:::-;694:20;;610:111;;;;;:::o;1702:188:27:-;1829:53;;-1:-1:-1;;;;;15254:32:66;;;1829:53:27;;;15236:51:66;15323:32;;;15303:18;;;15296:60;15372:18;;;15365:34;;;1802:81:27;;1822:5;;1844:18;;;;;15209::66;;1829:53:27;15034:371:66;2202:126:30;1897:4;1920:7;-1:-1:-1;;;1920:7:30;;;;2260:62;;2296:15;;-1:-1:-1;;;2296:15:30;;;;;;;;;;;2705:151:28;2780:12;2811:38;2833:6;2841:4;2847:1;2780:12;3421;3435:23;3462:6;-1:-1:-1;;;;;3462:11:28;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;;;;3180:392;;;;;;:::o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:28;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:28;;-1:-1:-1;;;;;3556:32:66;;5121:24:28;;;3538:51:66;3511:18;;5121:24:28;3392:203:66;5041:119:28;-1:-1:-1;5180:10:28;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:28;;;;;;;;;;;14:131:66;-1:-1:-1;;;;;89:31:66;;79:42;;69:70;;135:1;132;125:12;150:247;209:6;262:2;250:9;241:7;237:23;233:32;230:52;;;278:1;275;268:12;230:52;317:9;304:23;336:31;361:5;336:31;:::i;402:388::-;470:6;478;531:2;519:9;510:7;506:23;502:32;499:52;;;547:1;544;537:12;499:52;586:9;573:23;605:31;630:5;605:31;:::i;:::-;655:5;-1:-1:-1;712:2:66;697:18;;684:32;725:33;684:32;725:33;:::i;:::-;777:7;767:17;;;402:388;;;;;:::o;987:118::-;1073:5;1066:13;1059:21;1052:5;1049:32;1039:60;;1095:1;1092;1085:12;1110:1864;1295:6;1303;1311;1319;1327;1335;1343;1351;1359;1367;1420:3;1408:9;1399:7;1395:23;1391:33;1388:53;;;1437:1;1434;1427:12;1388:53;1476:9;1463:23;1495:31;1520:5;1495:31;:::i;:::-;1545:5;-1:-1:-1;1602:2:66;1587:18;;1574:32;1615:33;1574:32;1615:33;:::i;:::-;1667:7;-1:-1:-1;1747:2:66;1732:18;;1719:32;;-1:-1:-1;1829:2:66;1814:18;;1801:32;1842:33;1801:32;1842:33;:::i;:::-;1894:7;-1:-1:-1;1974:3:66;1959:19;;1946:33;;-1:-1:-1;2057:3:66;2042:19;;2029:33;2071:30;2029:33;2071:30;:::i;:::-;2120:7;-1:-1:-1;2200:3:66;2185:19;;2172:33;;-1:-1:-1;2304:3:66;2289:19;;2276:33;2318;2276;2318;:::i;:::-;2370:7;-1:-1:-1;2428:3:66;2413:19;;2400:33;2456:18;2445:30;;2442:50;;;2488:1;2485;2478:12;2442:50;2517:22;;2564:1;;2633:4;2619:19;;2615:33;-1:-1:-1;2605:75:66;;2669:8;2659;2652:26;2605:75;-1:-1:-1;2699:22:66;;2744:18;2733:30;;2730:64;;;2783:8;2773;2766:26;2730:64;2829:4;2819:8;2815:19;2803:31;;2891:7;2884:4;2874:6;2871:1;2867:14;2857:8;2853:29;2849:40;2846:53;2843:73;;;2912:1;2909;2902:12;2843:73;2935:8;2925:18;;2962:6;2952:16;;;;;1110:1864;;;;;;;;;;;;;:::o;3161:226::-;3220:6;3273:2;3261:9;3252:7;3248:23;3244:32;3241:52;;;3289:1;3286;3279:12;3241:52;-1:-1:-1;3334:23:66;;3161:226;-1:-1:-1;3161:226:66:o;3600:382::-;3665:6;3673;3726:2;3714:9;3705:7;3701:23;3697:32;3694:52;;;3742:1;3739;3732:12;3694:52;3781:9;3768:23;3800:31;3825:5;3800:31;:::i;:::-;3850:5;-1:-1:-1;3907:2:66;3892:18;;3879:32;3920:30;3879:32;3920:30;:::i;4276:184::-;4346:6;4399:2;4387:9;4378:7;4374:23;4370:32;4367:52;;;4415:1;4412;4405:12;4367:52;-1:-1:-1;4438:16:66;;4276:184;-1:-1:-1;4276:184:66:o;4752:245::-;4819:6;4872:2;4860:9;4851:7;4847:23;4843:32;4840:52;;;4888:1;4885;4878:12;4840:52;4920:9;4914:16;4939:28;4961:5;4939:28;:::i;6526:539::-;6615:5;6622:6;6682:3;6669:17;6768:2;6764:7;6753:8;6737:14;6733:29;6729:43;6709:18;6705:68;6695:96;;6787:1;6784;6777:12;6695:96;6815:33;;6919:4;6906:18;;;-1:-1:-1;6867:21:66;;-1:-1:-1;6947:18:66;6936:30;;6933:50;;;6979:1;6976;6969:12;6933:50;7033:6;7030:1;7026:14;7010;7006:35;6999:5;6995:47;6992:67;;;7055:1;7052;7045:12;6992:67;6526:539;;;;;:::o;7070:295::-;7140:5;7199:3;7186:17;7285:2;7281:7;7270:8;7254:14;7250:29;7246:43;7226:18;7222:68;7212:96;;7304:1;7301;7294:12;7212:96;7326:33;;;;7070:295;-1:-1:-1;;7070:295:66:o;7370:::-;7440:5;7499:3;7486:17;7585:2;7581:7;7570:8;7554:14;7550:29;7546:43;7526:18;7522:68;7512:96;;7604:1;7601;7594:12;7670:5442;-1:-1:-1;;;;;8014:32:66;;;7996:51;;8083:32;;8078:2;8063:18;;8056:60;7984:2;8147;8132:18;;8125:30;;;7969:18;;8190:22;;;-1:-1:-1;8243:3:66;8293:1;8289:14;;;8274:30;;8270:40;;;8228:19;;8333:6;-1:-1:-1;8367:4716:66;8381:6;8378:1;8375:13;8367:4716;;;8446:22;;;-1:-1:-1;;8442:37:66;8430:50;;8503:58;8554:6;8546;8503:58;:::i;:::-;8652:16;;8681:21;;8600:2;8588:15;;8749:78;8823:2;8815:11;;8665:2;8749:78;:::i;:::-;8715:112;;8864:2;8859;8851:6;8847:15;8840:27;8893:6;8927:12;8919:6;8912:28;8974:2;8966:6;8962:15;8953:24;;9043:2;9027:12;9024:1;9020:20;9012:6;9008:33;9004:42;8990:56;;9075:12;9111:1;9125:3849;9141:12;9136:3;9133:21;9125:3849;;;9226:19;;;-1:-1:-1;;9222:33:66;9208:48;;9283:66;9340:8;9326:12;9283:66;:::i;:::-;9392:2;9384:6;9380:15;9440:2;9427:16;9460:33;9485:7;9460:33;:::i;:::-;-1:-1:-1;;;;;9525:33:66;9510:49;;9614:78;9688:2;9680:11;;9684:2;9614:78;:::i;:::-;9576:116;;9733:2;9728;9720:6;9716:15;9709:27;9766:6;9804:14;9796:6;9789:30;9857:2;9849:6;9845:15;9836:24;;9932:2;9914:14;9911:1;9907:22;9899:6;9895:35;9891:44;9877:58;;9968:14;10010:1;10028:2813;10044:14;10039:3;10036:23;10028:2813;;;10139:19;;;-1:-1:-1;;10135:33:66;10121:48;;10200:68;10259:8;10243:14;10200:68;:::i;:::-;10315:2;10307:6;10303:15;10367:2;10354:16;10391:33;10416:7;10391:33;:::i;:::-;-1:-1:-1;;;;;10460:33:66;10445:49;;10584:2;10576:11;;;10563:25;10616:15;;;10609:32;10700:78;10774:2;10766:11;;10580:2;10700:78;:::i;:::-;10662:116;;10823:2;10818;10810:6;10806:15;10799:27;10860:6;10902:14;10894:6;10887:30;10959:3;10951:6;10947:16;10938:25;;11039:3;11021:14;11018:1;11014:22;11006:6;11002:35;10998:45;10984:59;;11080:14;11126:1;11148:1544;11164:14;11159:3;11156:23;11148:1544;;;11267:19;;;-1:-1:-1;;11263:34:66;11249:49;;11333:68;11392:8;11376:14;11333:68;:::i;:::-;11478:16;;11519:23;;11640:2;11632:11;;;11619:25;11676:15;;;11669:32;11773:2;11765:11;;11752:25;11844:14;11840:23;;;-1:-1:-1;;11836:37:66;11812:62;;11802:90;;11888:1;11885;11878:12;11802:90;11932:27;12072:2;12059:16;;;11998:21;12114:18;12103:30;;12100:50;;;12146:1;12143;12136:12;12100:50;12211:6;12195:14;12191:27;12182:7;12178:41;12175:61;;;12232:1;12229;12222:12;12175:61;12285:2;12280;12272:6;12268:15;12261:27;12337:6;12332:2;12324:6;12320:15;12313:31;12409:6;12400:7;12394:3;12386:6;12382:16;12369:47;12479:1;12473:3;12464:6;12456;12452:19;12448:29;12441:40;12564:3;12557:2;12553:7;12548:2;12540:6;12536:15;12532:29;12524:6;12520:42;12516:52;12506:62;;;;12619:2;12609:8;12605:17;12593:29;;12667:2;12660:5;12656:14;12647:23;;11198:1;11193:3;11189:11;11182:18;;11148:1544;;;-1:-1:-1;12723:6:66;;-1:-1:-1;;;12776:2:66;12809:14;;;;12762:17;;;;;-1:-1:-1;;10078:1:66;10069:11;10028:2813;;;-1:-1:-1;12868:6:66;;-1:-1:-1;;;12917:2:66;12946:14;;;;12903:17;;;;;-1:-1:-1;;9173:1:66;9164:11;9125:3849;;;-1:-1:-1;12997:6:66;;-1:-1:-1;;;13038:2:66;13061:12;;;;13026:15;;;;;-1:-1:-1;;8403:1:66;8396:9;8367:4716;;;-1:-1:-1;13100:6:66;;7670:5442;-1:-1:-1;;;;;;;;7670:5442:66:o;13117:127::-;13178:10;13173:3;13169:20;13166:1;13159:31;13209:4;13206:1;13199:15;13233:4;13230:1;13223:15;13249:128;13316:9;;;13337:11;;;13334:37;;;13351:18;;:::i;14639:168::-;14712:9;;;14743;;14760:15;;;14754:22;;14740:37;14730:71;;14781:18;;:::i;14812:217::-;14852:1;14878;14868:132;;14922:10;14917:3;14913:20;14910:1;14903:31;14957:4;14954:1;14947:15;14985:4;14982:1;14975:15;14868:132;-1:-1:-1;15014:9:66;;14812:217::o;15410:412::-;15539:3;15577:6;15571:13;15602:1;15612:129;15626:6;15623:1;15620:13;15612:129;;;15724:4;15708:14;;;15704:25;;15698:32;15685:11;;;15678:53;15641:12;15612:129;;;-1:-1:-1;15796:1:66;15760:16;;15785:13;;;-1:-1:-1;15760:16:66;15410:412;-1:-1:-1;15410:412:66:o", "linkReferences": {}}, "methodIdentifiers": {"WETH()": "ad5c4648", "addAdaptor(address,address)": "3e3828e0", "addExecutor(address)": "1f5a0bbe", "isExecutorWhitelist(address)": "34a628a0", "isReceiverWhitelist(address)": "d0cd3bbc", "maxFeeRate()": "85c17830", "owner()": "8da5cb5b", "pause()": "8456cb59", "pauseExecutor(address)": "f96bd2e9", "paused()": "5c975abb", "removeExecutor(address)": "24788429", "renounceOwnership()": "715018a6", "setMaxFeeRate(uint256)": "8cda96de", "superWithdraw(address,address)": "24975fe3", "swap(address,address,uint256,address,uint256,bool,uint256,address,(uint256,(address,(address,uint256,(uint256,uint256,bytes)[])[])[])[])": "3cd3d569", "transferOwnership(address)": "f2fde38b", "unpause()": "3f4ba83a", "unpauseExecutor(address)": "12a52c5d", "updateFeeReceiver(address,bool)": "a77469c8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_routerOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_weth\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"executor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"add\",\"type\":\"bool\"}],\"name\":\"ExecutorUpdate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"fromToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"fromTokenAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"toToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"toTokenAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"}],\"name\":\"Swap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"WETH\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_executor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_adapter\",\"type\":\"address\"}],\"name\":\"addAdaptor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"executor\",\"type\":\"address\"}],\"name\":\"addExecutor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isExecutorWhitelist\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isReceiverWhitelist\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxFeeRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_executor\",\"type\":\"address\"}],\"name\":\"pauseExecutor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"executor\",\"type\":\"address\"}],\"name\":\"removeExecutor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_maxFeeRate\",\"type\":\"uint256\"}],\"name\":\"setMaxFeeRate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"superWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"executor\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"fromToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"fromTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"toToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minAmountOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"feeOnFromToken\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"feeRate\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"feeReceiver\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"percent\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"toToken\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address payable\",\"name\":\"adapter\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"percent\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"percent\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"swapType\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct Utils.SimpleSwap[]\",\"name\":\"swaps\",\"type\":\"tuple[]\"}],\"internalType\":\"struct Utils.Adapter[]\",\"name\":\"adapters\",\"type\":\"tuple[]\"}],\"internalType\":\"struct Utils.SinglePath[]\",\"name\":\"paths\",\"type\":\"tuple[]\"}],\"internalType\":\"struct Utils.MultiPath[]\",\"name\":\"paths\",\"type\":\"tuple[]\"}],\"name\":\"swap\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_executor\",\"type\":\"address\"}],\"name\":\"unpauseExecutor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"feeReceiver\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"add\",\"type\":\"bool\"}],\"name\":\"updateFeeReceiver\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"events\":{\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"addExecutor(address)\":{\"params\":{\"executor\":\"The executor contract address\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"removeExecutor(address)\":{\"params\":{\"executor\":\"The executor contract address\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"swap(address,address,uint256,address,uint256,bool,uint256,address,(uint256,(address,(address,uint256,(uint256,uint256,bytes)[])[])[])[])\":{\"params\":{\"executor\":\"The executor contract address\",\"feeOnFromToken\":\"If the fee should be charged on fromToken\",\"feeRate\":\"The fee rate to charge, feeRate/10000 will be charged to the feeReceiver\",\"feeReceiver\":\"The address to receive the fee\",\"fromToken\":\"The token to swap from\",\"fromTokenAmount\":\"The amount of fromToken to swap\",\"minAmountOut\":\"The minimum amount of toToken to receive\",\"paths\":\"The paths to swap\",\"toToken\":\"The token to swap to\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addExecutor(address)\":{\"notice\":\"Add executor to the whitelist\"},\"pause()\":{\"notice\":\"Pause the contract\"},\"removeExecutor(address)\":{\"notice\":\"Remove executor from the whitelist\"},\"superWithdraw(address,address)\":{\"notice\":\"used for emergency, generally there wouldn't be tokens left\"},\"swap(address,address,uint256,address,uint256,bool,uint256,address,(uint256,(address,(address,uint256,(uint256,uint256,bytes)[])[])[])[])\":{\"notice\":\"Swap tokens using the executor\"},\"unpause()\":{\"notice\":\"Unpause the contract\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Router.sol\":\"Router\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\",\":openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/\",\":solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xb2e5f50762c27fb4b123e3619c3c02bdcba5e515309382e5bfb6f7d6486510bd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1a4b83328c98d518a2699c2cbe9e9b055e78aa57fa8639f1b88deb8b3750b5dc\",\"dweb:/ipfs/QmXdcYj5v7zQxXFPULShHkR5p4Wa2zYuupbHnFdV3cHYtc\"]},\"src/Router.sol\":{\"keccak256\":\"0x37320dfb9801adc132af4a798599518b04530798939db238a6bfff7f0e2bbe3a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4cd83cfa5610022733ca6167c0b5f99a0bad9ec136411daeca4fea25570ff5f7\",\"dweb:/ipfs/QmXkvhnttL4CdVZLhrz5S6cST73RiktymruEx4yikbK8n4\"]},\"src/interface/IExecutor.sol\":{\"keccak256\":\"0x7db690aacb0f5fbd5621310752f496136c240b06a4c15078525ca67929d2b691\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2b3a8417ee56762e58856fc5a842496dc2329cad65e623bd84b7ec4136a4e2db\",\"dweb:/ipfs/QmVE2b9xojnyHqkNiwJNtBruTCGdERFS1dHmkuksWSuXY7\"]},\"src/interface/IWETH.sol\":{\"keccak256\":\"0x163f084ccc0d2f7eb2345210665c5570e5d0bda0d82481277f94df8357f8505c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0b0d47d55c0ea70760d2a670d5bc25acb1dbf3901e14de8be012362c00248ef8\",\"dweb:/ipfs/QmaGhrw5N8r8hxnUv79dmeASxYTR6AXda7sm68KwT8wbjM\"]},\"src/library/SignedDecimalMath.sol\":{\"keccak256\":\"0x233497183aa899ca4e89554379e507915aff81d7cb4b7e7782fc85ea9da275bf\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf3c82c5dc0694f62d2cb5e0deb4948aea89fcfbdb48996206b7fd4f69ff831c\",\"dweb:/ipfs/QmbwbtWR78g8Je9NT2HZTutyvARdAJfv7M8XrVitfHPEYe\"]},\"src/library/UniversalERC20.sol\":{\"keccak256\":\"0x318be675f5b178cf642829edb118497171de2b701ba9af728e60462b44e86f11\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3850b40fd10f3ac96fff2897fada053167c00277c56251f6916d6c356804de98\",\"dweb:/ipfs/QmePP9BTrVa7X692k9bfE24R2wFV7YrHyd5GKTawMKKkMg\"]},\"src/library/Utils.sol\":{\"keccak256\":\"0x3d64fc51f505397af492eff0564d4bec8f269dc5702babb0981a7fe1c7e6aecc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c7fa03c5eb71038a46fdbd2bd606ddbe7e5b08ed7a8e492c33988bb1868677ad\",\"dweb:/ipfs/QmNwW6x8EkAmh2CQmnf8aYThFAKMD4ZpbFRJ6uBDpEAoaL\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_routerOwner", "type": "address"}, {"internalType": "address", "name": "_weth", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [], "type": "error", "name": "EnforcedPause"}, {"inputs": [], "type": "error", "name": "ExpectedPause"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address", "indexed": false}, {"internalType": "bool", "name": "add", "type": "bool", "indexed": false}], "type": "event", "name": "ExecutorUpdate", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Paused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "address", "name": "fromToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "fromTokenAmount", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "toToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "toTokenAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "fee", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Unpaused", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "WETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_executor", "type": "address"}, {"internalType": "address", "name": "_adapter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addAdaptor"}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addExecutor"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isReceiver<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "maxFeeRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [{"internalType": "address", "name": "_executor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "pauseExecutor"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removeExecutor"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "uint256", "name": "_maxFeeRate", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setMaxFeeRate"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "superWithdraw"}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address"}, {"internalType": "address", "name": "fromToken", "type": "address"}, {"internalType": "uint256", "name": "fromTokenAmount", "type": "uint256"}, {"internalType": "address", "name": "toToken", "type": "address"}, {"internalType": "uint256", "name": "minAmountOut", "type": "uint256"}, {"internalType": "bool", "name": "feeOnFromToken", "type": "bool"}, {"internalType": "uint256", "name": "feeRate", "type": "uint256"}, {"internalType": "address", "name": "feeReceiver", "type": "address"}, {"internalType": "struct Utils.MultiPath[]", "name": "paths", "type": "tuple[]", "components": [{"internalType": "uint256", "name": "percent", "type": "uint256"}, {"internalType": "struct Utils.SinglePath[]", "name": "paths", "type": "tuple[]", "components": [{"internalType": "address", "name": "toToken", "type": "address"}, {"internalType": "struct Utils.Adapter[]", "name": "adapters", "type": "tuple[]", "components": [{"internalType": "address payable", "name": "adapter", "type": "address"}, {"internalType": "uint256", "name": "percent", "type": "uint256"}, {"internalType": "struct Utils.SimpleSwap[]", "name": "swaps", "type": "tuple[]", "components": [{"internalType": "uint256", "name": "percent", "type": "uint256"}, {"internalType": "uint256", "name": "swapType", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}]}]}]}], "stateMutability": "payable", "type": "function", "name": "swap"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}, {"inputs": [{"internalType": "address", "name": "_executor", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "unpauseExecutor"}, {"inputs": [{"internalType": "address", "name": "feeReceiver", "type": "address"}, {"internalType": "bool", "name": "add", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateFeeReceiver"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"addExecutor(address)": {"params": {"executor": "The executor contract address"}}, "owner()": {"details": "Returns the address of the current owner."}, "paused()": {"details": "Returns true if the contract is paused, and false otherwise."}, "removeExecutor(address)": {"params": {"executor": "The executor contract address"}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "swap(address,address,uint256,address,uint256,bool,uint256,address,(uint256,(address,(address,uint256,(uint256,uint256,bytes)[])[])[])[])": {"params": {"executor": "The executor contract address", "feeOnFromToken": "If the fee should be charged on fromToken", "feeRate": "The fee rate to charge, feeRate/10000 will be charged to the feeReceiver", "feeReceiver": "The address to receive the fee", "fromToken": "The token to swap from", "fromTokenAmount": "The amount of fromToken to swap", "minAmountOut": "The minimum amount of toToken to receive", "paths": "The paths to swap", "toToken": "The token to swap to"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addExecutor(address)": {"notice": "Add executor to the whitelist"}, "pause()": {"notice": "Pause the contract"}, "removeExecutor(address)": {"notice": "Remove executor from the whitelist"}, "superWithdraw(address,address)": {"notice": "used for emergency, generally there wouldn't be tokens left"}, "swap(address,address,uint256,address,uint256,bool,uint256,address,(uint256,(address,(address,uint256,(uint256,uint256,bytes)[])[])[])[])": {"notice": "Swap tokens using the executor"}, "unpause()": {"notice": "Unpause the contract"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "ds-test/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/", "openzeppelin-foundry-upgrades/=lib/openzeppelin-foundry-upgrades/src/", "solidity-stringutils/=lib/openzeppelin-foundry-upgrades/lib/solidity-stringutils/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Router.sol": "Router"}, "evmVersion": "paris", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xb2e5f50762c27fb4b123e3619c3c02bdcba5e515309382e5bfb6f7d6486510bd", "urls": ["bzz-raw://1a4b83328c98d518a2699c2cbe9e9b055e78aa57fa8639f1b88deb8b3750b5dc", "dweb:/ipfs/QmXdcYj5v7zQxXFPULShHkR5p4Wa2zYuupbHnFdV3cHYtc"], "license": "MIT"}, "src/Router.sol": {"keccak256": "0x37320dfb9801adc132af4a798599518b04530798939db238a6bfff7f0e2bbe3a", "urls": ["bzz-raw://4cd83cfa5610022733ca6167c0b5f99a0bad9ec136411daeca4fea25570ff5f7", "dweb:/ipfs/QmXkvhnttL4CdVZLhrz5S6cST73RiktymruEx4yikbK8n4"], "license": "BUSL-1.1"}, "src/interface/IExecutor.sol": {"keccak256": "0x7db690aacb0f5fbd5621310752f496136c240b06a4c15078525ca67929d2b691", "urls": ["bzz-raw://2b3a8417ee56762e58856fc5a842496dc2329cad65e623bd84b7ec4136a4e2db", "dweb:/ipfs/QmVE2b9xojnyHqkNiwJNtBruTCGdERFS1dHmkuksWSuXY7"], "license": "BUSL-1.1"}, "src/interface/IWETH.sol": {"keccak256": "0x163f084ccc0d2f7eb2345210665c5570e5d0bda0d82481277f94df8357f8505c", "urls": ["bzz-raw://0b0d47d55c0ea70760d2a670d5bc25acb1dbf3901e14de8be012362c00248ef8", "dweb:/ipfs/QmaGhrw5N8r8hxnUv79dmeASxYTR6AXda7sm68KwT8wbjM"], "license": "BUSL-1.1"}, "src/library/SignedDecimalMath.sol": {"keccak256": "0x233497183aa899ca4e89554379e507915aff81d7cb4b7e7782fc85ea9da275bf", "urls": ["bzz-raw://cf3c82c5dc0694f62d2cb5e0deb4948aea89fcfbdb48996206b7fd4f69ff831c", "dweb:/ipfs/QmbwbtWR78g8Je9NT2HZTutyvARdAJfv7M8XrVitfHPEYe"], "license": "BUSL-1.1"}, "src/library/UniversalERC20.sol": {"keccak256": "0x318be675f5b178cf642829edb118497171de2b701ba9af728e60462b44e86f11", "urls": ["bzz-raw://3850b40fd10f3ac96fff2897fada053167c00277c56251f6916d6c356804de98", "dweb:/ipfs/QmePP9BTrVa7X692k9bfE24R2wFV7YrHyd5GKTawMKKkMg"], "license": "BUSL-1.1"}, "src/library/Utils.sol": {"keccak256": "0x3d64fc51f505397af492eff0564d4bec8f269dc5702babb0981a7fe1c7e6aecc", "urls": ["bzz-raw://c7fa03c5eb71038a46fdbd2bd606ddbe7e5b08ed7a8e492c33988bb1868677ad", "dweb:/ipfs/QmNwW6x8EkAmh2CQmnf8aYThFAKMD4ZpbFRJ6uBDpEAoaL"], "license": "BUSL-1.1"}}, "version": 1}, "id": 33}