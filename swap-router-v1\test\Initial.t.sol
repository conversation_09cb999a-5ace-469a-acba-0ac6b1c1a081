/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import {Test, console} from "forge-std/Test.sol";
import "forge-std/console.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../src/router/Router.sol";
import "../src/executor/Executor.sol";
import "../src/adapter/mainnet/Adapter1.sol";

interface Cheats {
    function expectRevert() external;

    function expectRevert(bytes calldata) external;
}

contract InitialTest is Test {
    // add this to be excluded from coverage report
    function test() public {}

    using SafeERC20 for IERC20;

    Cheats internal constant cheats = Cheats(0x7109709ECfa91a80626fF3989D68f67F5b1DD12D);
    Executor public executor;
    Router public router;
    Adapter1 public adapter1;
    address public feeReceiver = ******************************************;
    address[3] public admins =
        [******************************************, address(this), ******************************************];

    function setUp() public {
        router = new Router(
            // owner
            admins,
            // maxFeeRate
            1e16
        );
        adapter1 = new Adapter1(
            // dai
            ******************************************,
            // weth
            ******************************************,
            // permit2
            ******************************************
        );
        router.updateAdaptor(address(adapter1), true);
    }

    receive() external payable {}
}
