/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";

contract Adapter1Test is InitialTest {
    function testCurveV1WrongSwapType() public {
        CurveV1SwapArg memory arg = CurveV1SwapArg(******************************************, 0, 1, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 13, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        deal(******************************************, address(this), 100e18);
        IERC20(******************************************).approve(address(router), 100 ether);

        cheats.expectRevert("Executor: Invalid swap type");
        router.swap(
            ******************************************,
            100 ether,
            ******************************************,
            99e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }

    function testCurveV1WrongAdapterPercentage() public {
        CurveV1SwapArg memory arg = CurveV1SwapArg(******************************************, 0, 1, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(7e17, 3, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        deal(******************************************, address(this), 100e18);
        IERC20(******************************************).approve(address(router), 100 ether);

        cheats.expectRevert("Adaptor: Invalid total percent");
        router.swap(
            ******************************************,
            100 ether,
            ******************************************,
            99e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }
}
