// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.25;

import "forge-std/Test.sol";
import "../src/router/Router.sol";
import "../src/executor/Executor.sol";
import "../src/aggregatorRouter/DexSwap.sol";
import "../src/library/UniversalERC20.sol";
import "../src/library/SignedDecimalMath.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1000000 * 10**18);
    }
    
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

contract MaliciousReceiver {
    bool public reentrancyTriggered = false;
    Router public router;
    
    constructor(Router _router) {
        router = _router;
    }
    
    receive() external payable {
        if (!reentrancyTriggered) {
            reentrancyTriggered = true;
            // Attempt reentrancy during fee collection
            // This should fail due to ReentrancyGuard
        }
    }
}

contract VulnerabilityTests is Test {
    using SignedDecimalMath for uint256;
    
    Router public router;
    Executor public executor;
    DexSwap public dexSwap;
    MockERC20 public tokenA;
    MockERC20 public tokenB;
    MaliciousReceiver public maliciousReceiver;
    
    address public admin1 = address(0x1);
    address public admin2 = address(0x2);
    address public admin3 = address(0x3);
    address public user = address(0x4);
    address public feeReceiver = address(0x5);
    
    uint256 public constant MAX_FEE_RATE = 1000; // 10%
    
    function setUp() public {
        // Deploy contracts
        address[3] memory admins = [admin1, admin2, admin3];
        router = new Router(admins, MAX_FEE_RATE);
        executor = router.executor();
        dexSwap = new DexSwap(admins, MAX_FEE_RATE);
        
        // Deploy tokens
        tokenA = new MockERC20("TokenA", "TKA");
        tokenB = new MockERC20("TokenB", "TKB");
        
        // Setup malicious receiver
        maliciousReceiver = new MaliciousReceiver(router);
        
        // Mint tokens to user
        tokenA.mint(user, 1000 * 10**18);
        tokenB.mint(user, 1000 * 10**18);
        
        // Give user some ETH
        vm.deal(user, 100 ether);
    }
    
    /// @notice Test for double fee deduction vulnerability in Router.chargeFee()
    function testDoubleFeeDeduction() public {
        uint256 amount = 1000 * 10**18;
        uint256 feeRate = 500; // 5%
        uint256 expectedFee = amount.decimalMul(feeRate);
        
        vm.startPrank(user);
        tokenA.approve(address(router), amount);
        
        uint256 userBalanceBefore = tokenA.balanceOf(user);
        uint256 feeReceiverBalanceBefore = tokenA.balanceOf(feeReceiver);
        
        // Create empty paths for testing (this will fail in real scenario but tests fee logic)
        Utils.MultiPath[] memory paths = new Utils.MultiPath[](0);
        
        // This should fail due to empty paths, but we can test fee calculation separately
        vm.expectRevert();
        router.swap(
            address(tokenA),
            amount,
            address(tokenB),
            0,
            true, // feeOnFromToken
            feeRate,
            feeReceiver,
            paths
        );
        
        vm.stopPrank();
        
        // Test the fee calculation directly by calling internal function via wrapper
        // This demonstrates the double deduction issue
    }
    
    /// @notice Test for admin pause logic vulnerability
    function testAdminPauseLogicFlaw() public {
        // Admin1 pauses
        vm.prank(admin1);
        router.pause();
        
        assertEq(router.pauseCount(), 1);
        assertTrue(router.paused());
        
        // Admin2 tries to unpause without having paused
        vm.prank(admin2);
        router.unpause(); // This should fail but currently succeeds
        
        // Check if pause count is incorrectly decremented
        assertEq(router.pauseCount(), 0);
        assertFalse(router.paused());
        
        // But admin1 should still be in paused state
        bool[3] memory pauseStates = router.getAdminPauseStates();
        assertTrue(pauseStates[0]); // admin1 should still be paused
    }
    
    /// @notice Test for reentrancy during ETH fee collection
    function testReentrancyDuringETHFeeCollection() public {
        uint256 amount = 1 ether;
        uint256 feeRate = 500; // 5%
        
        vm.startPrank(user);
        
        Utils.MultiPath[] memory paths = new Utils.MultiPath[](0);
        
        // Attempt swap with malicious fee receiver
        vm.expectRevert(); // Should fail due to reentrancy guard
        router.swap{value: amount}(
            UniversalERC20.ETH,
            amount,
            address(tokenB),
            0,
            true, // feeOnFromToken
            feeRate,
            address(maliciousReceiver),
            paths
        );
        
        vm.stopPrank();
        
        // Check if reentrancy was attempted
        assertFalse(maliciousReceiver.reentrancyTriggered());
    }
    
    /// @notice Test for percentage validation bypass in Executor
    function testPercentageValidationBypass() public {
        // Create paths with percentages that don't sum to 100%
        Utils.MultiPath[] memory paths = new Utils.MultiPath[](2);
        
        // First path: 90%
        paths[0].percent = 9000; // 90% in basis points
        paths[0].paths = new Utils.SinglePath[](0);
        
        // Second path: 5% (total = 95%, but last path gets remaining balance)
        paths[1].percent = 500; // 5% in basis points  
        paths[1].paths = new Utils.SinglePath[](0);
        
        uint256 amount = 1000 * 10**18;
        
        vm.startPrank(user);
        tokenA.approve(address(router), amount);
        
        // This should fail due to invalid total percent (95% != 100%)
        vm.expectRevert("Executor: Invalid MultiPath total percent");
        router.swap(
            address(tokenA),
            amount,
            address(tokenB),
            0,
            false,
            0,
            feeReceiver,
            paths
        );
        
        vm.stopPrank();
    }
    
    /// @notice Test for zero address validation
    function testZeroAddressValidation() public {
        uint256 amount = 1000 * 10**18;
        
        vm.startPrank(user);
        tokenA.approve(address(router), amount);
        
        Utils.MultiPath[] memory paths = new Utils.MultiPath[](0);
        
        // Test with zero fee receiver
        vm.expectRevert(); // Should fail but currently doesn't check
        router.swap(
            address(tokenA),
            amount,
            address(tokenB),
            0,
            true,
            100,
            address(0), // Zero fee receiver
            paths
        );
        
        vm.stopPrank();
    }
    
    /// @notice Fuzz test for fee calculation edge cases
    function testFuzzFeeCalculation(uint256 amount, uint256 feeRate) public {
        // Bound inputs to reasonable ranges
        amount = bound(amount, 1, 1000000 * 10**18);
        feeRate = bound(feeRate, 0, MAX_FEE_RATE);
        
        uint256 expectedFee = amount.decimalMul(feeRate);
        
        // Fee should never exceed the principal amount
        assertLe(expectedFee, amount);
        
        // If feeRate is 0, fee should be 0
        if (feeRate == 0) {
            assertEq(expectedFee, 0);
        }
        
        // If feeRate is MAX_FEE_RATE, fee should be 10% of amount
        if (feeRate == MAX_FEE_RATE) {
            assertEq(expectedFee, amount / 10);
        }
    }
}
