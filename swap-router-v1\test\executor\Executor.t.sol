/*
    Copyright Debank
    SPDX-License-Identifier: BUSL-1.1
*/
pragma solidity ^0.8.25;

import "../Initial.t.sol";

contract ExecutorTest is InitialTest {
    function testExecutorRevert() public {
        CurveV1SwapArg memory arg = CurveV1SwapArg(******************************************, 0, 1, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 3, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 1e18, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(9e17, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        deal(******************************************, address(this), 100e18);
        IERC20(******************************************).approve(address(router), 100 ether);

        cheats.expectRevert("Executor: Invalid MultiPath total percent");
        router.swap(
            ******************************************,
            100 ether,
            ******************************************,
            99e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );

        adapters = new Utils.Adapter[](1);
        adapter = Utils.Adapter(payable(feeReceiver), 1e18, swaps);
        adapters[0] = adapter;
        singlePath0 = Utils.SinglePath(******************************************, adapters);
        singlePaths[0] = singlePath0;
        multiPath = Utils.MultiPath(9e17, singlePaths);
        multiPaths[0] = multiPath;

        cheats.expectRevert("Executor: adapter not whitelist");
        router.swap(
            ******************************************,
            100 ether,
            ******************************************,
            99e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }

    function testAdapterPercentageRevert() public {
        CurveV1SwapArg memory arg = CurveV1SwapArg(******************************************, 0, 1, 0);
        Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
        bytes memory payload = abi.encode(arg);
        Utils.SimpleSwap memory simpleSwap = Utils.SimpleSwap(1e18, 3, payload);
        swaps[0] = simpleSwap;
        Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
        Utils.Adapter memory adapter = Utils.Adapter(payable(adapter1), 9e17, swaps);
        adapters[0] = adapter;
        Utils.SinglePath memory singlePath0 = Utils.SinglePath(******************************************, adapters);
        Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
        singlePaths[0] = singlePath0;
        Utils.MultiPath memory multiPath = Utils.MultiPath(1e18, singlePaths);
        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](1);
        multiPaths[0] = multiPath;
        deal(******************************************, address(this), 100e18);
        IERC20(******************************************).approve(address(router), 100 ether);

        cheats.expectRevert("Executor: Invalid adapter total percent");
        router.swap(
            ******************************************,
            100 ether,
            ******************************************,
            99e6,
            false,
            0,
            feeReceiver,
            multiPaths
        );
    }

    function testCrossMultiPathTokenInterference() public {
        // Test the fix for cross-MultiPath token interference
        // This test verifies that each MultiPath uses only its allocated tokens

        // Initialize executor
        executor = new Executor();
        executor.updateAdaptor(address(adapter1), true);

        // Setup: 10 wETH to USDC with two MultiPaths
        // MultiPath[0]: 60% wETH -> PEPE -> wETH -> USDC
        // MultiPath[1]: 40% wETH -> USDC

        // Create MultiPath[0]: wETH -> PEPE -> wETH -> USDC (60%)
        Utils.MultiPath memory multiPath0;
        {
            Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](3);

            // wETH -> PEPE u2
            Utils.SimpleSwap[] memory swap1 = new Utils.SimpleSwap[](1);
            bytes memory payload = abi.encode(UniswapV2SwapArg(******************************************, 3, 1000));
            swap1[0] = Utils.SimpleSwap(1e18, 1, payload);
            Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
            adapters[0] = Utils.Adapter(payable(adapter1), 1e18, swap1);
            singlePaths[0] = Utils.SinglePath(******************************************, adapters); // PEPE

            // PEPE -> wETH u3
            Utils.SimpleSwap[] memory swaps2 = new Utils.SimpleSwap[](1);
            payload = abi.encode(Utils.UniswapV3Data(******************************************, 0, 3000));
            swaps2[0] = Utils.SimpleSwap(1e18, 5, payload);
            Utils.Adapter[] memory adapters2 = new Utils.Adapter[](1);
            adapters2[0] = Utils.Adapter(payable(adapter1), 1e18, swaps2);
            singlePaths[1] = Utils.SinglePath(******************************************, adapters2); // wETH

            // wETH -> USDC
            Utils.SimpleSwap[] memory swaps3 = new Utils.SimpleSwap[](1);
            payload = abi.encode(UniswapV2SwapArg(******************************************, 3, 1000));
            swaps3[0] = Utils.SimpleSwap(1e18, 1, payload);
            Utils.Adapter[] memory adapters3 = new Utils.Adapter[](1);
            adapters3[0] = Utils.Adapter(payable(adapter1), 1e18, swaps3);
            singlePaths[2] = Utils.SinglePath(******************************************, adapters3); // USDC

            multiPath0 = Utils.MultiPath(6e17, singlePaths); // 60%
        }

        // Create MultiPath[1]: wETH -> USDC (40%)
        Utils.MultiPath memory multiPath1;
        {
            Utils.SinglePath[] memory singlePaths = new Utils.SinglePath[](1);
            Utils.SimpleSwap[] memory swaps = new Utils.SimpleSwap[](1);
            bytes memory payload = abi.encode(Utils.UniswapV3Data(******************************************, 0, 3000));
            swaps[0] = Utils.SimpleSwap(1e18, 5, payload);
            Utils.Adapter[] memory adapters = new Utils.Adapter[](1);
            adapters[0] = Utils.Adapter(payable(adapter1), 1e18, swaps);
            singlePaths[0] = Utils.SinglePath(******************************************, adapters); // USDC

            multiPath1 = Utils.MultiPath(4e17, singlePaths); // 40%
        }

        Utils.MultiPath[] memory multiPaths = new Utils.MultiPath[](2);
        multiPaths[0] = multiPath0;
        multiPaths[1] = multiPath1;

        // Execute the swap
        deal(******************************************, address(this), 10 ether);
        IERC20(******************************************).approve(address(router), 10 ether);

        router.swap(
            ******************************************,
            10 ether,
            ******************************************,
            0,
            true,
            0,
            feeReceiver,
            multiPaths
        );
    }
}
